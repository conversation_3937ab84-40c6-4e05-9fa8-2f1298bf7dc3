// ========================================================== 查询渠道start =========================================

let P8QuickGameSDK_VERSION = "1.0.0"; // 2025-5-21 16:33:06
console.log('[聚合SDK] 版本号', P8QuickGameSDK_VERSION);
let Global;
if (typeof window != "undefined") { Global = window }
else if (typeof GameGlobal != "undefined") { Global = GameGlobal }
else if (typeof global != "undefined") { Global = global }
else { Global = {}; console.log("[聚合SDK] 未知渠道,请联系SDK开发者") }

// ========================================================== 查询渠道end ===========================================

// ========================================================== 查询平台start =========================================

if (typeof qg != "undefined") { Global.channel = "QuickGame"; Global.pf = "quickGame"; Global.PFSDK = qg }
else { Global.channel = ""; Global.PFSDK = window; console.log("[聚合SDK] 未知平台,请联系SDK开发者") }

// ========================================================== 查询平台end ===========================================

// ========================================================== 数据start ===============================================
let P8_QuickGame_Data = {
  systeminfo: {}, // 系统信息
  launchOptions: {}, // 启动参数
  engineVersion: '', // 引擎版本
  model: '', // 设备型号
  device_model: '', // 设备型号
  device_version: '', // 设备版本
  device_resolution: '', // 设备分辨率
  device_net: '', // 设备网络
  platform: '', // 平台
  channelid: '', // 渠道id
  site: '', // site
  key: '', // key
  aid: '', // aid
  appid: '', // appid
  pkgName: '', // 包名
  game_id: '', // 游戏id
  appName: '', // 游戏名
  data_url: '', // 日志url
  rg_url: '', // 支付url
  platform_url: '', // 平台url
  dsj_url: '', // 大数据url
  extraInfo: '', // 额外信息
  appKey: '', // 应用key
  appSecret: '', // 应用secret
  backcallUrl: '', // 回调url
  playerId: '', // 玩家id
  device: '', // 设备id
  uid: '', // 用户id
  account: '', // 用户名
  token: '', // 用户token
  mac: '', // 设备mac
  ip: '', // 设备ip
  modeltype: 'android', // 设备类型
  gameversion: '', // 游戏版本
  sid: '', // 区服id
  roleid: '', // 角色id
  rolename: '', // 角色名
  level: '', // 等级
  vip: '', // vip
  CPid: '', // 商户id
  publicKey: '', // 公钥
  appKey: '', // 应用key
  appSecret: '', // 应用secret
  backcallUrl: '', // 回调url
  nonce: '', // 随机数
  // 广告
  ad_positon: "",
  ad_unit_id: "",
  ad_slot: "",
  adList: {
    ad_unit_id_reward: "",
    ad_slot_reward: "",
  },
};

// ========================================================== 数据end =================================================

// ========================================================== 初始化start ===========================================
let PFSDK = Global.PFSDK;
let P8QuickGameSDK = {};
let P8QuickGameLogSDK = {};
let P8OppoSDK = {};
let p8Vivosdk = {};
let P8HonorSDK = {};

let start_param = "";
let ad_show_time = 0;
let queryData = {
  weixinadinfo: "",
  gdt_vid: "",
  aid: "",
  code: "",
  c: "",
};
let loginRetryTimer = null;

let qg_videoAD;
// 添加广告状态管理
let adLoadState = {
  isLoading: false,
  isReady: false
};
// 添加激励视频状态锁
let isVideoLogReported = false;
let isVideoShowInProgress = false;
let lastVideoShowTime = 0;
const VIDEO_SHOW_COOLDOWN = 1000; // 1秒冷却时间
let lastVideoReportTime = 0; // 添加静态变量存储上一次上报时间

console.log('[ PFSDK ] >', PFSDK)

function init() {
  if (!!PFSDK.getSystemInfoSync) {
    P8_QuickGame_Data.systeminfo = PFSDK.getSystemInfoSync();
    P8_QuickGame_Data.engineVersion = P8_QuickGame_Data.systeminfo.platformVersionCode;
    P8_QuickGame_Data.model = P8_QuickGame_Data.systeminfo.model;
    P8_QuickGame_Data.device_model = P8_QuickGame_Data.systeminfo.model;
    P8_QuickGame_Data.device_version = P8_QuickGame_Data.systeminfo.system;
    P8_QuickGame_Data.device_resolution = P8_QuickGame_Data.systeminfo.screenWidth + "*" + P8_QuickGame_Data.systeminfo.screenHeight;
    P8_QuickGame_Data.device_net = P8_QuickGame_Data.systeminfo.wifiSignal;
  } else {
    P8_QuickGame_Data.systeminfo = {}
  }

  if (!!PFSDK.getLaunchOptionsSync) {
    P8_QuickGame_Data.launchOptions = PFSDK.getLaunchOptionsSync()
  } else {
    P8_QuickGame_Data.launchOptions = {}
  }

  if (!!PFSDK.getNetworkType) {
    if (!P8_QuickGame_Data.device_net) {
      console.log('[聚合SDK] 获取不到网络类型,从getNetworkType获取')
      PFSDK.getNetworkType({
        success: function (res) {
          console.log('[聚合SDK] 获取网络类型成功 >', res.networkType)
          P8_QuickGame_Data.device_net = res.networkType;
        },
        fail: function (res) {
          console.log('[聚合SDK] 获取网络类型失败 >', res.errMsg);
        },
        complete: function (res) { },
      });
    }
  } else {
    P8_QuickGame_Data.device_net = "";
  }


  console.log('[ P8_QuickGame_Data ] >', P8_QuickGame_Data)
  console.log('[ P8_QuickGame_Data ] >', advancedObjectToString(P8_QuickGame_Data))
}

init();

// ========================================================== 初始化end ===============================================

// ========================================================== 快游戏SDK初始化 ===============================================

P8QuickGameSDK.init = function (a) {
  var t = ["aid", "appid", "key", "site", "pkgName", "platform", "channelid"];
  for (const r of t) {
    if (!a[r] || a[r] == "") {
      var n = {
        result: 1,
        data: {
          msg: `${r}不能为空`
        }
      };
      return new Promise((e, a) => {
        e(n)
      })
    }
  }
  P8_QuickGame_Data.site = a.site;
  P8_QuickGame_Data.key = a.key;
  P8_QuickGame_Data.aid = a.aid;
  P8_QuickGame_Data.channelid = a.channelid;
  P8_QuickGame_Data.appid = a.appid;
  P8_QuickGame_Data.appName = a.appName;
  P8_QuickGame_Data.pkgName = a.pkgName;
  P8_QuickGame_Data.game_id = a.aid.slice(-4);
  P8_QuickGame_Data.platform = a.platform;
  P8QuickGameSDK.print("site:" + a.site);
  P8QuickGameSDK.print("key:" + a.key);
  P8QuickGameSDK.print("aid:" + a.aid);
  P8QuickGameSDK.print("appid:" + a.appid);
  P8QuickGameSDK.print("appName:" + a.appName);
  P8QuickGameSDK.print("pkgName:" + a.pkgName);
  P8QuickGameSDK.print("game_id:" + P8_QuickGame_Data.game_id);
  P8QuickGameSDK.print("platform:" + P8_QuickGame_Data.platform);
  P8QuickGameSDK.print("channelid:" + P8_QuickGame_Data.channelid);

  return new Promise((resolve, reject) => {
    resolve({
      result: 0,
      data: {
        msg: '初始化成功'
      }
    })
  })
}

// ========================================================== 快游戏SDK初始化end ===============================================

// ========================================================== 登录start ===============================================

P8QuickGameSDK.login = function (_retryCount = 0, _maxRetries = 3, _retryDelay = 1000) {
  // 清除之前的重试定时器（如果存在）
  if (loginRetryTimer) {
    console.log('[聚合SDK] 清除之前的登录重试定时器');
    clearTimeout(loginRetryTimer);
    loginRetryTimer = null;
  }

  let result = new Promise((resolve, reject) => {
    console.log('[ P8_QuickGame_Data.platform ] >', P8_QuickGame_Data.platform)
    if (!P8_QuickGame_Data.platform) {
      resolve({
        result: 1,
        data: {
          msg: '请重新初始化SDK,platform平台不能为空'
        }
      })
      return;
    }

    // 平台SDK映射
    const platformSDKMap = {
      "oppo": P8OppoSDK,
      "vivo": p8Vivosdk,
      "honor": P8HonorSDK
    };

    // 获取当前平台对应的SDK
    const currentSDK = platformSDKMap[P8_QuickGame_Data.platform];

    // 如果找到对应平台的SDK，则调用其Login方法
    if (currentSDK) {
      currentSDK.Login().then((res) => {
        // 检查登录是否成功
        if (res.result !== 0 && _retryCount < _maxRetries) {
          console.log(`[聚合SDK] 登录失败，${_retryDelay / 1000}秒后进行第${_retryCount + 1}次重试...`);

          // 延迟后重试
          loginRetryTimer = setTimeout(() => {
            // 重置定时器引用
            loginRetryTimer = null;

            P8QuickGameSDK.login(_retryCount + 1, _maxRetries, _retryDelay)
              .then(retryRes => resolve(retryRes))
              .catch(err => reject(err));
          }, _retryDelay);
        } else {
          // 登录成功或已达到最大重试次数
          if (res.result !== 0) {
            console.log(`[聚合SDK] 登录失败，已重试${_retryCount}次，不再重试`);
          } else if (_retryCount > 0) {
            console.log(`[聚合SDK] 第${_retryCount}次重试登录成功`);
          }
          resolve(res);
        }
      }).catch(err => {
        // 处理异常情况
        console.error(`[登录错误] ${P8_QuickGame_Data.platform}登录失败:`, err);

        // 异常情况也进行重试
        if (_retryCount < _maxRetries) {
          console.log(`[聚合SDK] 登录异常，${_retryDelay / 1000}秒后进行第${_retryCount + 1}次重试...`);

          loginRetryTimer = setTimeout(() => {
            // 重置定时器引用
            loginRetryTimer = null;

            P8QuickGameSDK.login(_retryCount + 1, _maxRetries, _retryDelay)
              .then(retryRes => resolve(retryRes))
              .catch(retryErr => reject(retryErr));
          }, _retryDelay);
        } else {
          resolve({
            result: 1,
            data: {
              msg: `${P8_QuickGame_Data.platform}登录失败，已重试${_retryCount}次`,
              error: err
            }
          });
        }
      });
    } else {
      // 处理未知平台的情况
      console.error(`[登录错误] 未知平台: ${P8_QuickGame_Data.platform}`);
      resolve({
        result: 1,
        data: {
          msg: `未知平台: ${P8_QuickGame_Data.platform}`
        }
      });
    }
  })

  // 添加Promise完成后的清理逻辑
  result.finally(() => {
    // 如果这是最外层调用（非递归调用），确保在Promise完成后清理定时器
    if (_retryCount === 0 && loginRetryTimer) {
      clearTimeout(loginRetryTimer);
      loginRetryTimer = null;
    }
  });

  return result;
}

// ========================================================== 登录end =================================================

// ========================================================== 支付start =================================================

P8QuickGameSDK.pay = function (a) {
  console.log('[ 进入支付 ] >', advancedObjectToString(a))
  let result = new Promise((resolve, reject) => {
    console.log('[ P8_QuickGame_Data.platform ] >', P8_QuickGame_Data.platform)

    if (!P8_QuickGame_Data.platform) {
      resolve({
        result: 1,
        data: {
          msg: '请重新初始化SDK,platform平台不能为空'
        }
      })
      return;
    }

    // 平台SDK映射
    const platformSDKMap = {
      "oppo": P8OppoSDK,
      "vivo": p8Vivosdk,
      "honor": P8HonorSDK
    };

    // 获取当前平台对应的SDK
    const currentSDK = platformSDKMap[P8_QuickGame_Data.platform];

    // 如果找到对应平台的SDK，则调用其pay方法
    if (currentSDK) {
      currentSDK.pay(a).then((res) => {
        resolve(res);
      }).catch(err => {
        // 添加错误处理
        console.error(`[支付错误] ${P8_QuickGame_Data.platform}支付失败:`, err);
        resolve({
          result: 1,
          data: {
            msg: `${P8_QuickGame_Data.platform}支付失败`,
            error: err
          }
        });
      });
    } else {
      // 处理未知平台的情况
      console.error(`[支付错误] 未知平台: ${P8_QuickGame_Data.platform}`);
      resolve({
        result: 1,
        data: {
          msg: `未知平台: ${P8_QuickGame_Data.platform}`
        }
      });
    }
  })

  return result;
}

// ========================================================== 支付end =================================================

// ========================================================== 广告start =================================================

P8QuickGameSDK.videoADInit = function (a) {
  let result = new Promise((resolve, reject) => {
    if (!P8_QuickGame_Data.platform) {
      console.error('[聚合SDK] 广告初始化错误 平台不能为空')
      return;
    }

    // 平台SDK映射
    const platformSDKMap = {
      "oppo": P8OppoSDK,
      "vivo": p8Vivosdk,
      "honor": P8HonorSDK
    };

    // 获取当前平台对应的SDK
    const currentSDK = platformSDKMap[P8_QuickGame_Data.platform];

    // 如果找到对应平台的SDK，则调用其adInitalization方法
    if (currentSDK) {
      currentSDK.adInitalization(a);
    } else {
      // 处理未知平台的情况
      console.error(`[广告初始化错误] 未知平台: ${P8_QuickGame_Data.platform}`);
      resolve({
        result: 1,
        data: {
          msg: `未知平台: ${P8_QuickGame_Data.platform}`
        }
      });
    }
  })

  return result;
}

P8QuickGameSDK.videoADShow = function (a, b, c, d, e) {
  if (!P8_QuickGame_Data.platform) {
    console.error('[聚合SDK] 广告播放错误 平台不能为空')
    return;
  }

  // 平台SDK映射
  const platformSDKMap = {
    "oppo": P8OppoSDK,
    "vivo": p8Vivosdk,
    "honor": P8HonorSDK
  };

  // 获取当前平台对应的SDK
  const currentSDK = platformSDKMap[P8_QuickGame_Data.platform];

  // 如果找到对应平台的SDK，则调用其videoADShow方法
  if (currentSDK) {
    currentSDK.videoADShow(
      () => a(),
      () => b(),
      (err) => c(err),
      () => d(),
      e
    );
  }
}


// ========================================================== 广告end =================================================


// ========================================================== oppo SDK start =================================================
P8OppoSDK.Login = function () {
  let result = new Promise((resolve, reject) => {
    let a = "https://center.play800.cn/fast_game_callback/getaliyuntxt";
    P8OppoSDK.XmlHttpRequest(a + "?url=https://ks3-cn-shanghai.ksyun.com/aliyun/93615202390552966.txt", "GET", null, a => {
      console.log("请求术良数据" + JSON.stringify(a));
      if (a.url_address) {
        console.log('[ 获取服务器数据 ] >', a)
        var t = a.url_address.data_url;
        var n = a.url_address.rg_url;
        var i = a.url_address.platform_url;
        var dsj = a.url_address.dsj_url || "https://adv2.hntengy.cn";
        let e = a.extraInfo;
        P8Log("开始获取服务器数据 " + getTime());
        P8Log("data_url=" + t + ", rg_url=" + n + ", platform_url=" + i + ", dsj_url=" + dsj);
        Object.assign(P8_QuickGame_Data, {
          data_url: t,
          rg_url: n,
          platform_url: i,
          dsj_url: dsj,
          extraInfo: e
        });
        P8OppoSDK.getMerchantData().then((e) => {
          console.log('[ getMerchantData ] >', e)
          let a = e.userInfo ? e.userInfo : null;
          if (!a) {
            resolve({
              result: 1,
              data: {
                msg: "获取oppo userInfo 数据失败"
              }
            })
            return;
          }

          let t = a.userId;
          P8_QuickGame_Data.playerId = t;
          P8_QuickGame_Data.device = t;
          let n = `${P8_QuickGame_Data.platform_url}/api/createChannelUser`;
          let i = parseInt((new Date).getTime() / 1e3);
          var r = hex_md5(`${P8_QuickGame_Data.key}WX${P8_QuickGame_Data.site}WX${i}${i}`);
          let o = {
            channelid: P8_QuickGame_Data.channelid,
            aid: P8_QuickGame_Data.aid,
            site: P8_QuickGame_Data.site,
            channelUid: t,
            adid: t,
            udid: t,
            channelUserName: "",
            sign: r,
            time: i
          };
          P8Log("XmlHttpRequest  ********+ url=" + n + ", data==" + JSON.stringify(o));
          P8OppoSDK.XmlHttpRequest(n, "POST", o, e => {
            P8Log("play800登录返回 res=" + JSON.stringify(e));
            if (e.result == 0) {
              P8Log("获取uid成功~" + getTime());
              P8_QuickGame_Data.uid = e.data.uid;
              P8_QuickGame_Data.account = e.data.username;
              e.userInfo = a;
              console.log(" play800  log 2025 -02/13 login : ", JSON.stringify(e));
              resolve(e)
            } else {
              P8Log("获取aid失败~");
              resolve(e)
            }
          })
        })
      } else {
        let e = {
          result: 1,
          data: {
            msg: "获取域名数据失败"
          }
        };
        resolve(e);
        console.log("获取服务器域名失败")
      }
    })
  })

  return result;
}

P8OppoSDK.getMerchantData = function () {
  let e = new Promise((c, e) => {
    P8Log("获取服务器数据1");
    let t = `${P8_QuickGame_Data.platform_url}/fast_game_callback/getchannelother`;
    let a = {
      site: P8_QuickGame_Data.site,
      channel: P8_QuickGame_Data.channelid,
      version: "1.0",
      function: "getconfig"
    };
    P8OppoSDK.XmlHttpRequest(t + "?data=" + JSON.stringify(a), "GET", null, a => {
      if (a.result == 0) {
        P8Log("获取服务参数成功" + getTime() + "传入参数:" + t + "?data=");
        let e = a.data[0];
        P8_QuickGame_Data.appKey = e.appkey;
        P8_QuickGame_Data.appSecret = e.appsecret;
        P8_QuickGame_Data.backcallUrl = e.backcallUrl;
        P8_QuickGame_Data.extraInfo = e.extraInfo;
        P8Log("术良data" + JSON.stringify(a));
        P8Log("术良res" + JSON.stringify(e));
        P8Log("appKey" + e.appkey);
        P8Log("appSecret" + e.appsecret);
        qg.login({
          success: function (e) {
            P8_QuickGame_Data.token = e.token;
            console.log("qg.login 返回的res", JSON.stringify(e));
            var a = e.data;
            let t = (new Date)
              .getTime();
            console.log(" P8_QuickGame_Data.appKey :", P8_QuickGame_Data.appKey);
            console.log(" P8_QuickGame_Data.appSecret :", P8_QuickGame_Data.appSecret);
            let n = "appKey=" + P8_QuickGame_Data.appKey + "&" + "appSecret=" + P8_QuickGame_Data.appSecret + "&" + "pkgName=" + P8_QuickGame_Data.pkgName + "&" + "timeStamp=" + t + "&" + "token=" + a.token;
            let i = hex_md5(n);
            i = i.toLocaleUpperCase();
            console.log("md5 要转换成大写:", i);
            let r = "https://play.open.oppomobile.com/instant-game-open/userInfo";
            let o = "pkgName=" + P8_QuickGame_Data.pkgName + "&timeStamp=" + t + "&token=" + a.token + "&sign=" + i + "&version=1.0.0";
            console.log("请求登陆的参数：" + JSON.stringify(o));
            P8OppoSDK.XmlHttpRequest(r + "?" + o, "GET", null, e => {
              console.log("返回的玩家信息:" + JSON.stringify(e));
              c(e)
            })
          },
          fail: function (e) {
            c(e);
            console.log("登陆失败数据", JSON.stringify(e))
          }
        })
      }
    })
  });
  return e
}

P8OppoSDK.pay = function (o) {
  let result = new Promise((resolve, reject) => {
    let c = (new Date).getTime();
    let t = {
      appId: P8_QuickGame_Data.appid,
      openId: P8_QuickGame_Data.token,
      timestamp: c,
      productName: o.productName,
      productDesc: o.productDesc,
      count: o.count,
      price: o.price,
      currency: "CNY",
      callBackUrl: P8_QuickGame_Data.backcallUrl,
      cpOrderId: o.cpOrderId,
      appVersion: o.appVersion,
      engineVersion: P8_QuickGame_Data.engineVersion,
      model: P8_QuickGame_Data.model,
      attach: o.attach ? o.attach : "附加信息"
    };

    P8Log("执行pay 2025/05/15");
    P8Log("传入的支付参数:" + JSON.stringify(t));

    let p8PayTime = (new Date).getTime();
    let p8PaySign = hex_md5(`${P8_QuickGame_Data.site}${p8PayTime}${P8_QuickGame_Data.key}`);
    let p8PayParams = {
      serverId: o.serverId,
      username: o.userName,
      grade: o.grade,
      amount: o.price * o.count,
      desc: o.productDesc,
      productDesc: o.productDesc,
      productName: o.productName,
      orderId: o.cpOrderId,
      roleName: o.userName,
      aid: P8_QuickGame_Data.aid,
      uid: P8_QuickGame_Data.uid,
      site: P8_QuickGame_Data.site,
      channel: P8_QuickGame_Data.channelid,
      device: P8_QuickGame_Data.playerId,
      roleId: o.roleId,
      subject: P8_QuickGame_Data.appName,
      extraInfo: P8_QuickGame_Data.extraInfo,
      time: p8PayTime,
      sign: p8PaySign
    }

    P8OppoSDK.madeOrder(p8PayParams).then((res) => {
      if (res.result == 0) {
        P8Log(" P8下单成功: " + JSON.stringify(res));
        let a = {
          site: P8_QuickGame_Data.site,
          channel: P8_QuickGame_Data.channelid,
          version: "1.0",
          function: "getsign"
        };
        Object.assign(a, t);
        let r = `${P8_QuickGame_Data.platform_url}/fast_game_callback/getchannelother`;
        console.log("sign 请求参数:" + JSON.stringify(a));
        P8OppoSDK.XmlHttpRequest(r + "?data=" + JSON.stringify(a), "GET", null, a => {
          console.log("请求术良sign返回的是" + JSON.stringify(a));
          if (a.result == 0) {
            console.log("获取服务sign成功" + a.data.sign + getTime());
            t.sign = a.data.sign;
            console.log("支付请求参数 : ", JSON.stringify(t));
            let e = "https://jits.open.oppomobile.com/jitsopen/api/pay/v1.0/preOrder";
            P8OppoSDK.XmlHttpRequest(e, "POST", t, a => {
              console.log("oppo 支付返回的数据" + JSON.stringify(a));
              if (a.code == 200) {
                let t = a.data;
                let e = {
                  site: P8_QuickGame_Data.site,
                  channel: P8_QuickGame_Data.channelid,
                  version: "1.0",
                  function: "getsign",
                  appKey: P8_QuickGame_Data.appKey,
                  orderNo: t.orderNo,
                  timestamp: c
                };
                console.log("请求二次sign 请求参数:" + JSON.stringify(e));
                P8OppoSDK.XmlHttpRequest(r + "?data=" + JSON.stringify(e), "GET", null, a => {
                  console.log("请求术良二次sign返回的是" + JSON.stringify(a));
                  if (a.result == 0) {
                    console.log("获取 二次sign成功" + a.data.sign + getTime());
                    let e = a.data.sign;
                    qg.pay({
                      appId: P8_QuickGame_Data.appid,
                      token: P8_QuickGame_Data.token,
                      timestamp: c,
                      paySign: e,
                      orderNo: t.orderNo,
                      success: function (e) {
                        console.log("支付成功", JSON.stringify(e.data));
                        resolve(e)
                      },
                      fail: function (e) {
                        console.log("支付失败", JSON.stringify(e));
                        resolve(e)
                      }
                    })
                  } else {
                    console.log("二次获取失败")
                  }
                })
              } else {
                P8Log("下单没有拉起")
              }
            })
          }
        }, false)
      } else {
        P8Log(" P8下单失败: " + JSON.stringify(res));
      }
    })
  })

  return result;
}

P8OppoSDK.madeOrder = function (sdkParams) {
  P8Log(" P8下单参数: " + JSON.stringify(sdkParams));
  let result = new Promise((resolve, reject) => {
    let n = `${P8_QuickGame_Data.platform_url}/sdk/createorder`;
    P8OppoSDK.XmlHttpRequest(n, "POST", sdkParams, e => {
      console.log("p8调用下单返回的数据:" + JSON.stringify(e))
      resolve(e);
    })
  })
  return result
}

P8OppoSDK.adInitalization = function (e) {
  console.log("oppo 广告初始化");
  // 激励视频初始化
  if (e && (typeof e === "string" || typeof e === "object")) {
    const adUnitId = typeof e === "string" ? e : e.adUnitId;
    if (adUnitId) {
      console.log("激励视频初始化", typeof e === "string" ? adUnitId : JSON.stringify(e));
      // 保存广告ID
      P8_QuickGame_Data.adList.ad_unit_id_reward = adUnitId;
      if (typeof e === "object" && e.adSlot) {
        P8_QuickGame_Data.adList.ad_slot_reward = e.adSlot;
      }
      // 销毁广告实例
      destroyVideoAd();
      // 创建广告实例
      qg_videoAD = qg.createRewardedVideoAd({
        adUnitId: adUnitId,
      });
      // 统一的事件处理
      qg_videoAD.onLoad(() => {
        print("激励视频 广告加载事件成功");
        adLoadState.isLoading = false;
        adLoadState.isReady = true;
      });
      qg_videoAD.onError((err) => {
        print("激励视频 广告加载异常", err);
        adLoadState.isLoading = false;
        adLoadState.isReady = false;
      });
      // 初始加载
      if (!adLoadState.isLoading && !adLoadState.isReady) {
        adLoadState.isLoading = true;
        qg_videoAD.load().then(() => {
          print("激励视频 广告加载成功");
          adLoadState.isReady = true;
        }).catch(err => {
          print("激励视频 初始加载失败", err);
          adLoadState.isReady = false;
        }).finally(() => {
          adLoadState.isLoading = false;
        });
      }
    }
  }
};

P8OppoSDK.videoADShow = function (t, a, c, onShow, adp) {
  // 检查是否在冷却时间内
  const now = Date.now();

  if (now - lastVideoShowTime < VIDEO_SHOW_COOLDOWN) {
    print("广告展示太频繁，请稍后再试");
    return;
  }

  // 检查是否有广告正在展示
  if (isVideoShowInProgress) {
    print("广告正在展示中");
    return;
  }

  P8_QuickGame_Data.ad_positon = adp || "";
  if (!qg_videoAD || !qg_videoAD.show) {
    print("激励视频不存在");
    return;
  }

  // 在新视频开始时初始化计时值
  ad_show_time = new Date().getTime();

  // 设置广告正在展示状态
  isVideoShowInProgress = true;

  // 重置状态锁
  isVideoLogReported = false;

  // 更新计时值
  lastVideoShowTime = now;

  // 优化的展示逻辑
  const showAd = () => {
    qg_videoAD.show().then(() => {
      if (onShow) onShow(); // 广告成功展示时触发回调
    }).catch((e) => {
      print("激励视频 广告加载异常", e);
      isVideoShowInProgress = false; // 重置状态

      // 展示失败时尝试重新加载并展示
      if (!adLoadState.isLoading) {
        adLoadState.isLoading = true;
        qg_videoAD.load().then(() => {
          return qg_videoAD.show().then(() => {
            if (onShow) onShow(); // 重试成功后也要触发回调
          });
        }).catch((err) => {
          print("激励视频 重试失败", err);
          if (c) c(err);
        }).finally(() => {
          adLoadState.isLoading = false;
          isVideoShowInProgress = false;
        });
      } else {
        if (c) c(e);
      }
    });
  };

  // 移除所有已存在的事件监听器
  qg_videoAD.offClose();

  // 准备上报数据
  let arg = {
    type: "RewardedVideoAd",
    status: "0",
    geType: "reward",
  }

  P8_QuickGame_Data.ad_unit_id = P8_QuickGame_Data.adList.ad_unit_id_reward;
  P8_QuickGame_Data.ad_slot = P8_QuickGame_Data.adList.ad_slot_reward;

  // 上报视频status = 0 状态
  if (!isVideoLogReported) {
    debounce(P8QuickGameSDK.wxVideoAutoLog, 1000)(arg)
    isVideoLogReported = true;
  }

  const i = (e) => {
    qg_videoAD.offClose(i);
    isVideoShowInProgress = false; // 重置状态

    if ((e && e.isEnded) || e === undefined) {
      print("正常播放结束，可以下发游戏奖励");
      let succ = {
        type: "RewardedVideoAd",
        status: "1",
        geType: "reward",
      }

      // 重置状态锁并上报视频status = 1 完成状态
      isVideoLogReported = false;
      debounce(P8QuickGameSDK.wxVideoAutoLog, 1000)(succ)

      // 预加载下一个广告
      if (!adLoadState.isLoading) {
        adLoadState.isLoading = true;
        qg_videoAD.load().finally(() => {
          adLoadState.isLoading = false;
        });
      }

      if (t) t()
    } else {
      print("播放中途退出，不下发游戏奖励");
      if (a) a()
    }
  };

  // 添加事件监听器
  qg_videoAD.onClose(i);

  // 检查是否需要重新加载
  if (!adLoadState.isReady && !adLoadState.isLoading) {
    adLoadState.isLoading = true;
    qg_videoAD.load().then(() => {
      showAd();
    }).catch((err) => {
      print("激励视频 加载失败", err);
      if (c) c(err);
    }).finally(() => {
      adLoadState.isLoading = false;
      isVideoShowInProgress = false;
    });
  } else {
    showAd();
  }
};

P8OppoSDK.XmlHttpRequest = function (e, a = "post", t = {}, n = null, i = true) {
  console.log('[ e ] >', e)
  let r = new XMLHttpRequest;
  r.open(a, e);
  if (i) {
    r.setRequestHeader("Content-type", "application/json")
  } else {
    console.log("术良 url" + e);
    r.setRequestHeader("Content-type", "application/x-www-form-urlencoded")
  }
  r.send(JSON.stringify(t));
  r.onreadystatechange = function () {
    if (r.status == 200 && r.readyState == 4) {
      let e = JSON.parse(r.responseText);
      console.log("请求返回的数据是什么:" + JSON.stringify(e));
      if (n) {
        n(e)
      }
    }
  }
}

// ========================================================== oppo SDK end =================================================

// ========================================================== vivo SDK start =================================================

p8Vivosdk.Login = function () {
  let i = new Promise((c, e) => {
    let a = "https://center.play800.cn/fast_game_callback/getaliyuntxt";
    console.log("请求术良 vivo 数据");
    p8Vivosdk.XmlHttpRequest(a + "?url=https://ks3-cn-shanghai.ksyun.com/aliyun/936640176220913570.txt", "GET", null, a => {
      console.log("请求术良 vivo 数据" + advancedObjectToString(a));
      if (a.url_address) {
        var t = a.url_address.data_url;
        var n = a.url_address.rg_url;
        var i = a.url_address.platform_url;
        let dsj_url = a.url_address.dsj_url || 'https://adv2.hntengy.cn';
        let e = a.extInfo;
        P8Log("开始获取服务器数据 " + getTime());
        P8Log("data_url=" + t + ", rg_url=" + n + ", platform_url=" + i + ", dsj_url=" + dsj_url);
        Object.assign(P8_QuickGame_Data, {
          data_url: t,
          rg_url: n,
          platform_url: i,
          extInfo: e,
          dsj_url: dsj_url
        });
        p8Vivosdk.GetMerchantData().then(e => {
          let a = e ? e : null;
          if (!a) {
            c({
              result: 1,
              data: {
                msg: "获取vivo userInfo 数据失败"
              }
            })
          }
          let t = e.openId;
          P8_QuickGame_Data.playerId = t;
          P8_QuickGame_Data.device = t;
          let n = `${P8_QuickGame_Data.platform_url}/api/createChannelUser`;
          let i = parseInt((new Date)
            .getTime() / 1e3);
          var r = hex_md5(`${P8_QuickGame_Data.key}WX${P8_QuickGame_Data.site}WX${i}${i}`);
          let o = {
            channelid: P8_QuickGame_Data.channelid,
            aid: P8_QuickGame_Data.aid,
            site: P8_QuickGame_Data.site,
            channelUid: t,
            adid: t,
            udid: t,
            channelUserName: "",
            sign: r,
            time: i
          };
          P8Log("XmlHttpRequest  2021+ url=" + n + ", data==" + JSON.stringify(o));
          p8Vivosdk.XmlHttpRequest(n, "POST", o, e => {
            P8Log("play800登录返回 res=" + JSON.stringify(e));
            if (e.result == 0) {
              P8Log("获取uid成功~" + getTime());
              P8_QuickGame_Data.uid = e.data.uid;
              e.vivo_data = a;
              console.log(" play800  log 2021 -06/30 login : ", JSON.stringify(e));
              c(e)
            } else {
              P8Log("获取aid失败~");
              c(e)
            }
          })
        })
      } else {
        let e = {
          result: 1,
          data: {
            msg: "获取数据失败"
          }
        };
        c(e);
        console.log("play800 init 获取服务器数据失败")
      }
    })
  });
  return i
}

p8Vivosdk.GetMerchantData = function () {
  let e = new Promise((c, e) => {
    P8Log("获取服务器数据1");
    let t = `https://tcenter.play800.cn/fast_game_callback/getchannelother`;
    let a = {
      site: P8_QuickGame_Data.site,
      channel: P8_QuickGame_Data.channelid,
      version: "1.0",
      function: "getconfig"
    };
    console.log("test == P8_QuickGame_Data : ", advancedObjectToString(P8_QuickGame_Data), ":", advancedObjectToString(a));
    console.log("test == P8_QuickGame_Data : ", advancedObjectToString(a));
    console.log("test == P8_QuickGame_Data e: ", JSON.stringify(e));
    console.log('[ 1 ] >', 1)
    p8Vivosdk.XmlHttpRequest(t + "?data=" + JSON.stringify(a), "GET", null, a => {
      console.log('[ a ] >', advancedObjectToString(a))
      if (a.result == 0) {
        console.log('[ 2 ] >', 2)
        P8Log("获取服务参数成功" + getTime() + "传入参数:" + t + "?data=");
        let e = a.data[0];
        P8_QuickGame_Data.appKey = e.appkey;
        P8_QuickGame_Data.appSecret = e.appsecret;
        P8_QuickGame_Data.backcallUrl = e.backcallUrl;
        P8_QuickGame_Data.nonce = getNonce();
        //P8_QuickGame_Data.signature = e.signature;
        P8Log("术良data" + JSON.stringify(a));
        P8Log("术良res" + JSON.stringify(e));
        P8Log("appKey" + e.appkey);
        P8Log("appSecret" + e.appsecret);
        if (qg.getSystemInfoSync().platformVersionCode >= 1063) {
          qg.login({
            success: function (e) {
              P8_QuickGame_Data.token = e.token;
              console.log("qg.login 返回的res", JSON.stringify(e));
              var a = e.data;
              let t = (new Date).getTime();
              let n = "appKey=" + P8_QuickGame_Data.appKey + "&" + "appSecret=" + P8_QuickGame_Data.appSecret + "&" + "nonce=" + P8_QuickGame_Data.nonce + "&" + "pkgName=" + P8_QuickGame_Data.pkgName + "&" + "timestamp=" + t + "&" + "token=" + a.token;
              console.log(" P8_QuickGame_Data.nonce :", P8_QuickGame_Data.nonce);
              console.log("-------------加密前", n);
              let i = sha256_digest(n);
              i = i.toLocaleUpperCase();
              //console.log("md5 要转换成大写:", i);
              let r = "https://quickgame.vivo.com.cn/api/quickgame/cp/account/userInfo";
              let o = "nonce=" + P8_QuickGame_Data.nonce + "&pkgName=" + P8_QuickGame_Data.pkgName + "&timestamp=" + t + "&token=" + a.token + "&signature=" + i; //+ "&version=1.0.0";
              console.log("请求登陆的参数：" + JSON.stringify(o));
              p8Vivosdk.XmlHttpRequest(r + "?" + o, "GET", null, e => {
                console.log("返回的玩家信息:" + JSON.stringify(e.data));
                c(e.data)
              })
            },
            fail: function (e) {
              c(e);
              console.log("登陆失败数据", JSON.stringify(e))
            }
          })
        }
      } else {

      }
    })
  });
  return e
}

p8Vivosdk.pay = function (o) {
  console.log('[ vivo 进入支付 ] >', advancedObjectToString(o))
  let c = (new Date)
    .getTime();
  let time = dateFormat();
  let params = {
    appId: P8_QuickGame_Data.appid,
    cpOrderNumber: o.cpOrderId,
    productName: o.productName,
    productDesc: o.productDesc,
    orderAmount: o.price,
    notifyUrl: P8_QuickGame_Data.backcallUrl,
    expireTime: time,
    extInfo: o.extInfo,
  };
  P8Log("执行pay 2025");

  let a = new Promise(function (i, e) {
    let a = {
      site: P8_QuickGame_Data.site,
      channel: P8_QuickGame_Data.channelid,
      version: "1.0",
      function: "getsign"
    };
    Object.assign(a, params);
    let r = `https://tcenter.play800.cn/fast_game_callback/getchannelother`;
    console.log("sign 请求参数:" + advancedObjectToString(a));
    p8Vivosdk.XmlHttpRequest(r + "?data=" + JSON.stringify(a), "GET", null, a => {
      console.log("请求术良sign返回的是" + advancedObjectToString(a));
      if (a.result == 0) {
        console.log("获取服务sign成功" + a.data.sign + getTime());
        params.vivoSignature = a.data.sign;
        let Object_vivo = {
          code: '',
          msg: ''
        }
        console.log("vivo 支付返回的数据" + advancedObjectToString(a));

        var e = hex_md5(`${P8_QuickGame_Data.site}${c}${P8_QuickGame_Data.key}`);
        let t = {
          serverId: o.serverId,
          username: o.userName,
          grade: o.grade,
          amount: o.price * o.count,
          desc: o.productDesc,
          productDesc: o.productDesc,
          productName: o.productName,
          orderId: o.cpOrderId,
          roleName: o.userName,
          aid: P8_QuickGame_Data.aid,
          uid: P8_QuickGame_Data.uid,
          site: P8_QuickGame_Data.site,
          channel: P8_QuickGame_Data.channelid,
          device: P8_QuickGame_Data.playerId,
          roleId: o.roleId,
          subject: P8_QuickGame_Data.appName,
          extraInfo: P8_QuickGame_Data.extraInfo,
          time: c,
          sign: e
        };
        P8Log(" 2025 支付8001 playerId : " + P8_QuickGame_Data.playerId);
        P8Log(" 2025 支付8001 appName : " + P8_QuickGame_Data.appName);
        P8Log(" 2025 支付8001 extraInfo : " + advancedObjectToString(P8_QuickGame_Data.extraInfo));
        P8Log(" 2025 支付8001 sdkParams : " + advancedObjectToString(t));
        let n = `${P8_QuickGame_Data.platform_url}/sdk/createorder`;
        p8Vivosdk.XmlHttpRequest(n, "POST", t, e => {
          console.log("p8这边调用下单参数:" + advancedObjectToString(e))
          if (e.result == 0) {
            qg.pay({
              orderInfo: JSON.stringify(params),
              success: function (ret) {
                console.log("支付成功", advancedObjectToString(ret));
                Object_vivo.code = 0;
                Object_vivo.msg = "支付成功";
                i(Object_vivo)
              },
              fail: function (err) {
                console.log("支付失败" + advancedObjectToString(err));
                Object_vivo.code = -2;
                Object_vivo.msg = "支付失败：" + advancedObjectToString(err);
                i(Object_vivo)
              },
              cancel: function (ret) {
                console.log("支付取消" + advancedObjectToString(ret))
                Object_vivo.code = -1;
                Object_vivo.msg = "支付取消";
                i(Object_vivo)
              },
              complete: function () {
                console.log("支付完成")
              }
            })
          } else {
            console.log("P8SDK下单失败");
          }
        })
      }
    }, false)
  });
  return a
}

p8Vivosdk.adInitalization = function (e) {
  console.log("vivo 广告初始化", JSON.stringify(e));
  // 激励视频初始化
  if (e && (typeof e === "string" || typeof e === "object")) {
    const adUnitId = typeof e === "string" ? e : e.adUnitId;
    if (adUnitId) {
      console.log("激励视频初始化", typeof e === "string" ? adUnitId : JSON.stringify(e));
      // 保存广告ID
      P8_QuickGame_Data.adList.ad_unit_id_reward = adUnitId;
      if (typeof e === "object" && e.adSlot) {
        P8_QuickGame_Data.adList.ad_slot_reward = e.adSlot;
      }
      // 销毁广告实例
      destroyVideoAd();
      // 创建广告实例
      qg_videoAD = qg.createRewardedVideoAd({
        posId: adUnitId,
      });
      // 统一的事件处理
      qg_videoAD.onLoad(() => {
        console.log("激励视频 广告加载事件成功");
        adLoadState.isLoading = false;
        adLoadState.isReady = true;
      });
      qg_videoAD.onError((err) => {
        console.log("激励视频 广告加载异常", JSON.stringify(err));
        adLoadState.isLoading = false;
        adLoadState.isReady = false;
      });
      // 初始加载
      if (!adLoadState.isLoading && !adLoadState.isReady) {
        adLoadState.isLoading = true;
        qg_videoAD.load().then(() => {
          console.log("激励视频 广告加载成功");
          adLoadState.isReady = true;
        }).catch(err => {
          console.log("激励视频 初始加载失败", JSON.stringify(err));
          adLoadState.isReady = false;
        }).finally(() => {
          adLoadState.isLoading = false;
        });
      }
    }
  }
};

p8Vivosdk.videoADShow = function (t, a, c, onShow, adp) {
  console.log("vivo 广告展示");
  // 检查是否在冷却时间内
  const now = Date.now();
  if (now - lastVideoShowTime < VIDEO_SHOW_COOLDOWN) {
    console.log("广告展示太频繁，请稍后再试");
    return;
  }
  // 检查是否有广告正在展示
  if (isVideoShowInProgress) {
    console.log("广告正在展示中");
    return;
  }
  P8_QuickGame_Data.ad_positon = adp || "";
  if (!qg_videoAD || !qg_videoAD.show) {
    console.log("激励视频不存在");
    return;
  }

  // 在新视频开始时初始化计时值
  ad_show_time = new Date().getTime();
  // 设置广告正在展示状态
  isVideoShowInProgress = true;
  // 重置状态锁
  isVideoLogReported = false;
  // 更新计时值
  lastVideoShowTime = now;
  // 优化的展示逻辑
  const showAd = () => {
    qg_videoAD.show().then(() => {
      if (onShow) onShow(); // 广告成功展示时触发回调
    }).catch((e) => {
      console.log("激励视频 广告加载异常", JSON.stringify(e));
      isVideoShowInProgress = false; // 重置状态
      // 展示失败时尝试重新加载并展示
      if (!adLoadState.isLoading) {
        adLoadState.isLoading = true;
        qg_videoAD.load().then(() => {
          return qg_videoAD.show().then(() => {
            if (onShow) onShow(); // 重试成功后也要触发回调
          });
        }).catch((err) => {
          console.log("激励视频 重试失败", JSON.stringify(err));
          if (c) c(err);
        }).finally(() => {
          adLoadState.isLoading = false;
          isVideoShowInProgress = false;
        });
      } else {
        if (c) c(e);
      }
    });
  };

  // 移除所有已存在的事件监听器
  qg_videoAD.offClose();

  P8_QuickGame_Data.ad_unit_id = P8_QuickGame_Data.adList.ad_unit_id_reward;
  P8_QuickGame_Data.ad_slot = P8_QuickGame_Data.adList.ad_slot_reward;
  // 上报视频status = 0 状态
  if (!isVideoLogReported) {
    isVideoLogReported = true;
  }
  const i = (e) => {
    qg_videoAD.offClose(i);
    isVideoShowInProgress = false; // 重置状态
    let arg = {
      type: "RewardedVideoAd",
      status: "0",
      geType: "reward",
    }
    debounce(P8QuickGameSDK.wxVideoAutoLog, 1000)(arg)

    if ((e && e.isEnded) || e === undefined) {
      console.log("正常播放结束，可以下发游戏奖励");
      let succ = {
        type: "RewardedVideoAd",
        status: "1",
        geType: "reward",
      }
      // 重置状态锁并上报视频status = 1 完成状态
      isVideoLogReported = false;
      setTimeout(() => {
        debounce(P8QuickGameSDK.wxVideoAutoLog, 1000)(succ)
      }, 1000)

      // 预加载下一个广告
      if (!adLoadState.isLoading) {
        adLoadState.isLoading = true;
        qg_videoAD.load().finally(() => {
          adLoadState.isLoading = false;
        });
      }
      if (t) t()
    } else {
      console.log("播放中途退出，不下发游戏奖励");
      if (a) a()
    }
  };

  // 添加事件监听器
  qg_videoAD.onClose(i);

  // 检查是否需要重新加载
  if (!adLoadState.isReady && !adLoadState.isLoading) {
    adLoadState.isLoading = true;
    qg_videoAD.load().then(() => {
      showAd();
    }).catch((err) => {
      console.log("激励视频 加载失败", JSON.stringify(err));
      if (c) c(err);
    }).finally(() => {
      adLoadState.isLoading = false;
      isVideoShowInProgress = false;
    });
  } else {
    showAd();
  }
};

p8Vivosdk.XmlHttpRequest = function (e, a = "post", t = {}, n = null, i = true) {
  let r = new XMLHttpRequest;
  r.open(a, e);
  if (i) {
    r.setRequestHeader("Content-type", "application/json")
  } else {
    console.log("术良 url" + e);
    r.setRequestHeader("Content-type", "application/x-www-form-urlencoded")
  }
  r.send(JSON.stringify(t));

  r.onreadystatechange = function () {
    console.log("-----------------测试", r.status, r.readyState);
    if (r.status == 200 && r.readyState == 4) {
      let e = JSON.parse(r.responseText);
      console.log("请求返回的数据是什么:" + JSON.stringify(e));
      if (n) {
        n(e)
      }
    }
  }
}

p8Vivosdk.qgRequest = function (e, t = "post", a = {}, s = null, f = null) {
  qg.request({
    url: e,
    method: t,
    data: a,
    dataType: "json",
    success: (success) => {
      if (s) {
        s(success);
      }
    },
    fail: (fail) => {
      if (f) {
        f(fail);
      }
    },
  });
}

// ========================================================== vivo SDK end =================================================

// ========================================================== 荣耀 SDK start =================================================

P8HonorSDK.Login = async function (a) {
  let result = new Promise(async (resolve, reject) => {
    try {
      const urlConfigRes = await P8HonorSDK.getUrlConfig();
      console.log('[ 域名初始化成功 ] >', JSON.stringify(urlConfigRes));

      if (urlConfigRes.result !== 0) {
        resolve(urlConfigRes);
        return;
      }

      qg.login({
        needAuthCode: true,
        success: async (res) => {
          console.log('[ 荣耀获取antuCode成功 ] >', JSON.stringify(res));
          const { authCode, openId, nickname, avatarUrl, unionId } = res;
          P8_QuickGame_Data.openid = openId;
          P8_QuickGame_Data.device = openId;
          P8_QuickGame_Data.nickname = nickname;
          P8_QuickGame_Data.avatarUrl = avatarUrl;
          P8_QuickGame_Data.unionid = unionId;
          P8_QuickGame_Data.device_code = openId;
          // 荣耀登录
          let time = parseInt(new Date().getTime() / 1e3);
          let url = `${P8_QuickGame_Data.platform_url}/mGame/login/${P8_QuickGame_Data.channelid}`
          let login_data = {
            site: P8_QuickGame_Data.site,
            appid: P8_QuickGame_Data.appid,
            js_code: authCode,
            openid: P8_QuickGame_Data.openid,
            unionid: P8_QuickGame_Data.unionid,
            aid: P8_QuickGame_Data.aid,
            code: queryData.code,
            channel_parame: start_param,
            time: time,
          }
          ChangeUndefined(login_data);
          let n = newSignGetType_log(login_data);
          console.log('[ n ] >', n)
          let d = hex_md5(n);
          login_data.sign = d;
          console.log('[ play800登录请求参数 ] >', JSON.stringify(login_data));
          httpRequest({
            url: url,
            method: 'POST',
            data: login_data,
          }).then(response => {
            console.log(' [ play800登录返回 ] > ', JSON.stringify(response));
            if (response.result == 0) {
              let e = response.data;
              P8_QuickGame_Data.uid = e.uid + '';
              P8_QuickGame_Data.account = e.account;
              P8_QuickGame_Data.password = e.password;
              P8_QuickGame_Data.sessionid = e.sessionid;
              P8_QuickGame_Data.istemp = e.istemp;
              P8_QuickGame_Data.sessiontime = e.sessiontime;
              print("登录成功回调 进行赋值", JSON.stringify(P8_QuickGame_Data));
              let cp = {
                result: 0,
                data: {
                  openid: P8_QuickGame_Data.openid,
                  uid: P8_QuickGame_Data.uid,
                  account: P8_QuickGame_Data.account,
                  password: P8_QuickGame_Data.password,
                  sessionid: e.sessionid,
                  sessiontime: e.sessiontime,
                }
              }
              Object.assign(cp.data, e);
              loginResult = cp;
              resolve(loginResult);
              P8HonorSDK.getPayConfig();
            } else {
              print("play800登录异常:" + JSON.stringify(response));
              resolve(response);
            }
          }).catch(error => {
            console.error('登录失败', JSON.stringify(error));
            reject(error);
          });
        },
        fail: (err) => {
          console.log('[ 荣耀获取antuCode失败 ] >', JSON.stringify(err));
          resolve(err);
        }
      });
    } catch (error) {
      reject(error);
    }
  });

  return result;
}

P8HonorSDK.getUrlConfig = function () {
  console.log('[ getUrlConfig ] >',)
  let p = new Promise((resolve, reject) => {
    let url = `https://ksyun.oss-cn-hangzhou.aliyuncs.com/${P8_QuickGame_Data.aid}.txt`;
    httpRequest({
      url: url,
      method: 'GET',
    }).then(response => {
      console.log('域名请求成功', JSON.stringify(response));
      let config = response.data || response;
      console.log('[ config ] >', JSON.stringify(config))
      if (config.url_address) {
        let data_url = config.url_address.data_url;
        let rg_url = config.url_address.rg_url;
        let platform_url = config.url_address.platform_url;
        let dsj_url = config.url_address.dsj_url;
        console.log("data_url=" + data_url + ", rg_url=" + rg_url + ", platform_url=" + platform_url + ", dsj_url=" + dsj_url);

        Object.assign(P8_QuickGame_Data, {
          data_url: data_url, // 上报url
          rg_url: rg_url, //// 支付url
          platform_url: platform_url,
          dsj_url: dsj_url,
        });

        var response = {
          result: 0,
          data: {
            msg: "域名初始化成功"
          }
        };

      } else {
        var response = {
          result: 1,
          data: {
            msg: "域名获取失败，请联系运营配置"
          }
        };
      }
      resolve(response);
    }).catch(error => {
      console.error('域名获取失败', JSON.stringify(error));
      reject(error);
    });
  })

  return p;
}

P8HonorSDK.getPayConfig = function () {
  console.log('[ P8HonorSDK getPayConfig] >',)
  let p = new Promise((resolve, reject) => {
    let url = `${P8_QuickGame_Data.platform_url}/mGame/payConf/${P8_QuickGame_Data.channelid}`
    let time = parseInt(new Date().getTime() / 1e3);
    let pay_data = {
      site: P8_QuickGame_Data.site,
      time: time,
    }
    ChangeUndefined(pay_data);
    let n = newSignGetType_log(pay_data);
    let d = hex_md5(n);
    pay_data.sign = d;
    console.log('[ play800支付配置参数请求 ] >', JSON.stringify(pay_data));
    httpRequest({
      url: url,
      method: 'GET',
      data: pay_data,
    }).then(res => {
      console.log(' [ play800支付配置参数返回 ] > ', JSON.stringify(res));
      if (res.result == 0 && res.data.is_set == true) {
        P8_QuickGame_Data.CPid = res.data.CPid;
        P8_QuickGame_Data.publicKey = res.data.publicKey;
        resolve();
      } else if (res.result == 0 && res.data.is_set == false) {
        console.log("支付配置参数未设置");
        resolve();
      } else if (res.result == 1) {
        console.log("支付配置参数接口请求失败");
      }
    }).catch(error => {
      console.error('支付配置参数接口请求失败', JSON.stringify(error));
      reject(error);
    });
  });
  return p;
}

P8HonorSDK.pay = async function (a) {
  let result = new Promise((resolve, reject) => {
    let time = parseInt(new Date().getTime() / 1e3);
    let pay_data = {
      site: P8_QuickGame_Data.site,
      aid: P8_QuickGame_Data.aid,
      uid: P8_QuickGame_Data.uid,
      cp_order_id: a.cp_order_id,
      roleid: a.roleid,
      rolename: a.rolename,
      level: a.level,
      serverid: a.serverid,
      productid: a.productid,
      product_name: a.product_name,
      money: a.money,
      ext: a.ext,
      time: time,
      ip: a.ip,
      udid: P8_QuickGame_Data.openid,
      test: a.test,
    }
    ChangeUndefined(pay_data);
    let n = newSignGetType_log(pay_data);
    let d = hex_md5(n);
    pay_data.sign = d;
    console.log('[ play800支付请求参数 ] >', JSON.stringify(pay_data));
    let url = `${P8_QuickGame_Data.rg_url}/mini/order/rongyao`
    httpRequest({
      url: url,
      method: 'GET',
      data: pay_data,
    }).then(res => {
      console.log(' [ play800支付返回 ] > ', JSON.stringify(res));
      if (res.result == 0) {
        const orderInfo = {
          appId: P8_QuickGame_Data.appid,
          cpId: P8_QuickGame_Data.CPid,
          productId: a.productid,
          publicKey: P8_QuickGame_Data.publicKey,
          productName: a.product_name,
          productDesc: a.product_name,
          orderAmount: Number(a.money) * 100,
          developerPayload: res.data.order_id,
          bizOrderNo: res.data.order_id,
          needSandboxTest: 0,
        }
        console.log('[ 荣耀支付请求参数 ] >', JSON.stringify(orderInfo));
        qg.pay({
          orderInfo,
          productInfo: orderInfo,
          success: function (ret) {
            console.log('支付成功: ' + JSON.stringify(ret))
            let res = {
              result: 0,
              data: {
                msg: "支付成功",
                data: ret
              }
            }
            resolve(res);
          },
          fail: function (err) {
            console.log('支付失败: ' + JSON.stringify(err))
            let res = {
              result: 1,
              data: {
                msg: "支付失败",
                data: err
              }
            }
            resolve(res);
          },
          cancel: function (ret) {
            console.log('支付取消: ' + JSON.stringify(ret))
            let res = {
              result: 1,
              data: {
                msg: "支付取消",
                data: ret
              }
            }
            resolve(res);
          },
        })
      } else {
        print("play800支付异常:" + JSON.stringify(res));
        resolve(res);
      }
    }).catch(error => {
      console.error('支付异常', JSON.stringify(error));
      reject(error);
    });
  });

  return result;
}

P8HonorSDK.adInitalization = function (e) {
  console.log("荣耀广告初始化");
  // 激励视频初始化
  if (e && (typeof e === "string" || typeof e === "object")) {
    const adUnitId = typeof e === "string" ? e : e.adUnitId;
    if (adUnitId) {
      console.log("激励视频初始化", typeof e === "string" ? adUnitId : JSON.stringify(e));

      // 保存广告ID
      P8_QuickGame_Data.adList.ad_unit_id_reward = adUnitId;
      if (typeof e === "object" && e.adSlot) {
        P8_QuickGame_Data.adList.ad_slot_reward = e.adSlot;
      }

      // 销毁广告实例
      destroyVideoAd();

      // 创建广告实例
      qg_videoAD = qg.createRewardedVideoAd({
        adUnitId: adUnitId,
      });

      // 统一的事件处理
      qg_videoAD.onLoad(() => {
        print("激励视频 广告加载事件成功");
        adLoadState.isLoading = false;
        adLoadState.isReady = true;
      });

      qg_videoAD.onError((err) => {
        print("激励视频 广告加载异常", err);
        adLoadState.isLoading = false;
        adLoadState.isReady = false;
      });

      // 初始加载
      if (!adLoadState.isLoading && !adLoadState.isReady) {
        adLoadState.isLoading = true;
        qg_videoAD.load().then(() => {
          print("激励视频 广告加载成功");
          adLoadState.isReady = true;
        }).catch(err => {
          print("激励视频 初始加载失败", err);
          adLoadState.isReady = false;
        }).finally(() => {
          adLoadState.isLoading = false;
        });
      }
    }
  }
};

P8HonorSDK.videoADShow = function (t, a, c, onShow, adp) {
  // 检查是否在冷却时间内
  const now = Date.now();

  if (now - lastVideoShowTime < VIDEO_SHOW_COOLDOWN) {
    print("广告展示太频繁，请稍后再试");
    return;
  }

  // 检查是否有广告正在展示
  if (isVideoShowInProgress) {
    print("广告正在展示中");
    return;
  }

  P8_QuickGame_Data.ad_positon = adp || "";
  if (!qg_videoAD || !qg_videoAD.show) {
    print("激励视频不存在");
    return;
  }

  // 在新视频开始时初始化计时值
  ad_show_time = new Date().getTime();

  // 设置广告正在展示状态
  isVideoShowInProgress = true;

  // 重置状态锁
  isVideoLogReported = false;

  // 更新计时值
  lastVideoShowTime = now;

  // 优化的展示逻辑
  const showAd = () => {
    qg_videoAD.show().then(() => {
      if (onShow) onShow(); // 广告成功展示时触发回调
    }).catch((e) => {
      print("激励视频 广告加载异常", e);
      isVideoShowInProgress = false; // 重置状态

      // 展示失败时尝试重新加载并展示
      if (!adLoadState.isLoading) {
        adLoadState.isLoading = true;
        qg_videoAD.load().then(() => {
          return qg_videoAD.show().then(() => {
            if (onShow) onShow(); // 重试成功后也要触发回调
          });
        }).catch((err) => {
          print("激励视频 重试失败", err);
          if (c) c(err);
        }).finally(() => {
          adLoadState.isLoading = false;
          isVideoShowInProgress = false;
        });
      } else {
        if (c) c(e);
      }
    });
  };

  // 移除所有已存在的事件监听器
  qg_videoAD.offClose();

  // 准备上报数据
  let arg = {
    type: "RewardedVideoAd",
    status: "0",
    geType: "reward",
  }

  P8_QuickGame_Data.ad_unit_id = P8_QuickGame_Data.adList.ad_unit_id_reward;
  P8_QuickGame_Data.ad_slot = P8_QuickGame_Data.adList.ad_slot_reward;

  // 上报视频status = 0 状态
  if (!isVideoLogReported) {
    debounce(P8QuickGameSDK.wxVideoAutoLog, 1000)(arg)
    isVideoLogReported = true;
  }

  const i = (e) => {
    qg_videoAD.offClose(i);
    isVideoShowInProgress = false; // 重置状态

    if ((e && e.isEnded) || e === undefined) {
      print("正常播放结束，可以下发游戏奖励");
      let succ = {
        type: "RewardedVideoAd",
        status: "1",
        geType: "reward",
      }

      // 重置状态锁并上报视频status = 1 完成状态
      isVideoLogReported = false;
      debounce(P8QuickGameSDK.wxVideoAutoLog, 1000)(succ)

      // 预加载下一个广告
      if (!adLoadState.isLoading) {
        adLoadState.isLoading = true;
        qg_videoAD.load().finally(() => {
          adLoadState.isLoading = false;
        });
      }

      if (t) t()
    } else {
      print("播放中途退出，不下发游戏奖励");
      if (a) a()
    }
  };

  // 添加事件监听器
  qg_videoAD.onClose(i);

  // 检查是否需要重新加载
  if (!adLoadState.isReady && !adLoadState.isLoading) {
    adLoadState.isLoading = true;
    qg_videoAD.load().then(() => {
      showAd();
    }).catch((err) => {
      print("激励视频 加载失败", err);
      if (c) c(err);
    }).finally(() => {
      adLoadState.isLoading = false;
      isVideoShowInProgress = false;
    });
  } else {
    showAd();
  }
};

// ========================================================== 荣耀 SDK end =================================================

// ========================================================== 公共行为上报方法start ===============================================

// SDK激活上报
P8QuickGameSDK.onActiveFunc = function (g) {
  let e = new Promise((r, e) => {
    let t = parseInt(new Date().getTime() / 1e3);
    let i = {
      site: P8_QuickGame_Data.site,
      aid: P8_QuickGame_Data.aid,
      time: t,
      device: P8_QuickGame_Data.device,
      ip: P8_QuickGame_Data.ip,
      mac: P8_QuickGame_Data.mac,
      modeltype: P8_QuickGame_Data.modeltype,
      gameversion: P8_QuickGame_Data.gameversion,
      device_model: P8_QuickGame_Data.device_model,
      device_resolution: P8_QuickGame_Data.device_resolution,
      device_version: P8_QuickGame_Data.device_version,
      device_net: P8_QuickGame_Data.device_net,
    };
    ChangeUndefined(i);
    let n = newSignGetType_log(i);
    var d = hex_md5(n);
    i.sign = d;
    let a = `${P8_QuickGame_Data.data_url}/log/activate`;
    console.log("  激活上报请求服务器参数: " + advancedObjectToString(i));
    let o = a + "?" + keyValueConnect(i);
    console.log("激活上报Url: ", o);
    qgRequest(a, "GET", i, (e) => {
      console.log(" 激活返回的数据 res： " + advancedObjectToString(e));
      let t = dateOrRes(e);
      console.log('[ t ] >', t)
      if (t.result == 0) {
        console.log("激活数据上报成功 ");
        var i = {
          result: "0",
          msg: "激活数据上报成功"
        };
      } else {
        console.log("激活数据上报失败 ");
        var i = {
          result: "1",
          data: {
            errorcode: t.data.errorcode,
            msg: t.data.msg
          }
        };
      }
      r(i);
      debounce(P8QuickGameSDK.dsjAotLog, 1000)(g, "activate");
    });
  });
  return e;
};

// 登录上报
P8QuickGameSDK.pushLoginData = function (s) {
  console.log(" =============== 开始登录上报: 传入的数据是 " + advancedObjectToString(s));
  let e = new Promise((r, e) => {
    let t = parseInt(new Date().getTime() / 1e3);
    let i = {
      aid: P8_QuickGame_Data.aid,
      uid: P8_QuickGame_Data.uid,
      sid: s.sid || P8_QuickGame_Data.sid || '1',
      roleid: s.roleid || P8_QuickGame_Data.roleid || P8_QuickGame_Data.uid || '1',
      rolename: s.rolename || P8_QuickGame_Data.rolename || P8_QuickGame_Data.uid || '1',
      level: s.level || P8_QuickGame_Data.level || '1',
      vip: s.vip || P8_QuickGame_Data.vip || '1',
      ip: s.ip || P8_QuickGame_Data.ip,
      onlinetime: s.onlinetime,
      device: P8_QuickGame_Data.device,
      modeltype: P8_QuickGame_Data.modeltype,
      device_model: P8_QuickGame_Data.device_model,
      device_resolution: P8_QuickGame_Data.device_resolution,
      device_version: P8_QuickGame_Data.device_version,
      device_net: P8_QuickGame_Data.device_net,
      oaid: s.oaid,
      site: P8_QuickGame_Data.site,
      time: t,
      version: P8QuickGameSDK_VERSION,
      game_type: 'mini'
    };
    P8_QuickGame_Data.sid = s.sid;
    P8_QuickGame_Data.roleid = s.roleid;
    P8_QuickGame_Data.rolename = s.rolename;
    P8_QuickGame_Data.level = s.level;
    P8_QuickGame_Data.vip = s.vip;
    setheartbeat(i);
    ChangeUndefined(i);
    let n = newSignGetType_log(i);
    var d = hex_md5(n);
    i.sign = d;
    let a = `${P8_QuickGame_Data.data_url}/log/login`;
    console.log(" 登录上报请求服务器参数: ", advancedObjectToString(i));
    let o = a + "?" + keyValueConnect(i);
    console.log("登录上报请求Url: ", o);
    qgRequest(a, "GET", i, (e) => {
      let t = dateOrRes(e);
      console.log("登录返回的数据 是什么 " + advancedObjectToString(t));
      if (t.result) {
        console.log("登录数据上报失败 ");
        var i = {
          result: "1",
          data: {
            errorcode: t.errorcode,
            msg: t.msg
          }
        };
      } else {
        console.log("登录数据上报成功 ");
        var i = {
          result: "0",
          data: {
            errorcode: 200,
            msg: "登录数据上报成功"
          }
        };
      }
      r(i);
      debounce(P8QuickGameSDK.dsjAotLog, 1000)(s, "login");
    });
  });
  return e;
};

// 创角上报
P8QuickGameSDK.signLog = function (o) {
  console.log(" =============== 开始创角上报: 传入的数据是 " + advancedObjectToString(o));
  let e = new Promise((r, e) => {
    ChangeUndefined(o);
    let s = o;
    if (!o) {
      s = {
        sid: "sid",
        uid: P8_QuickGame_Data.uid,
        roleid: "roleid",
        rolename: "rolename",
        device: "device",
        modeltype: "modeltype",
        mac: "mac",
        level: "level",
        gameversion: "gameversion",
        ip: "ip",
        device_model: "device_model",
        device_resolution: "device_resolution",
        device_version: "device_version",
        device_net: "device_net"
      };
    } else {
      s = {
        sid: o.sid || P8_QuickGame_Data.sid || '1',
        roleid: o.roleid || P8_QuickGame_Data.roleid || P8_QuickGame_Data.uid || '1',
        rolename: o.rolename || P8_QuickGame_Data.rolename || P8_QuickGame_Data.uid || '1',
        level: o.level || P8_QuickGame_Data.level || '1',
        gameversion: P8_QuickGame_Data.gameversion,
        uid: P8_QuickGame_Data.uid,
        device: P8_QuickGame_Data.device,
        modeltype: P8_QuickGame_Data.modeltype,
        mac: P8_QuickGame_Data.mac,
        ip: P8_QuickGame_Data.ip,
        device_model: P8_QuickGame_Data.device_model,
        device_resolution: P8_QuickGame_Data.device_resolution,
        device_version: P8_QuickGame_Data.device_version,
        device_net: P8_QuickGame_Data.device_net,
      }
    }
    s.game_type = 'mini';
    let t = parseInt(new Date().getTime() / 1e3);
    s.site = P8_QuickGame_Data.site;
    s.time = t;
    s.aid = P8_QuickGame_Data.aid;
    ChangeUndefined(s);
    let i = newSignGetType_log(s);
    var n = hex_md5(i);
    s.sign = n;
    let d = `${P8_QuickGame_Data.data_url}/log/role`;
    console.log("  创角上报请求服务器参数: " + advancedObjectToString(s));
    let a = d + "?" + keyValueConnect(s);
    console.log("创角上报请求Url: ", a);
    qgRequest(d, "GET", s, (e) => {
      let t = dateOrRes(e);
      console.log("创角返回的数据 是什么 " + advancedObjectToString(t));
      if (t.result == 0) {
        console.log(P8_QuickGame_Data.uid ? "创角数据上报成功 " : "创角数据上报成功,但是获取uid失败了,上报了没有uid的数据");
        var i = {
          result: P8_QuickGame_Data.uid ? 0 : 1,
          data: {
            errorcode: P8_QuickGame_Data.uid ? 0 : 200,
            msg: P8_QuickGame_Data.uid ? "创角数据上报成功" : "创角上报了,获取uid失败了,上报了没有uid的数据"
          }
        };
      } else {
        console.log("创角数据上报失败");
        var i = {
          result: 1,
          data: {
            errorcode: 200,
            msg: "创角数据上报失败"
          }
        };
      }
      r(i);
      debounce(P8QuickGameSDK.dsjAotLog, 1000)(s, "role_create");
    });
  });
  return e;
};

// 角色升级上报
P8QuickGameSDK.upGradeRecord = function (s) {
  console.log(" =============== 开始升级上报: 传入的数据是 " + advancedObjectToString(s));
  let e = new Promise((r, e) => {
    let t = parseInt(new Date().getTime() / 1e3);
    let i = {
      aid: P8_QuickGame_Data.aid,
      uid: P8_QuickGame_Data.uid,
      device: P8_QuickGame_Data.device,
      modeltype: P8_QuickGame_Data.modeltype,
      sid: s.sid || P8_QuickGame_Data.sid || '1',
      roleid: s.roleid || P8_QuickGame_Data.roleid || P8_QuickGame_Data.uid || '1',
      rolename: s.rolename || P8_QuickGame_Data.rolename || P8_QuickGame_Data.uid || '1',
      level: s.level || P8_QuickGame_Data.level || '1',
      vip: s.vip || P8_QuickGame_Data.vip || '1',
      ip: P8_QuickGame_Data.ip,
      onlinetime: s.onlinetime,
      device_model: P8_QuickGame_Data.device_model,
      device_resolution: P8_QuickGame_Data.device_resolution,
      device_version: P8_QuickGame_Data.device_version,
      device_net: P8_QuickGame_Data.device_net,
      oaid: s.oaid,
      site: P8_QuickGame_Data.site,
      time: t,
      version: P8QuickGameSDK_VERSION
    };
    P8_QuickGame_Data.sid = s.sid;
    P8_QuickGame_Data.roleid = s.roleid;
    P8_QuickGame_Data.rolename = s.rolename;
    P8_QuickGame_Data.level = s.level;
    P8_QuickGame_Data.vip = s.vip;
    ChangeUndefined(i);
    let n = newSignGetType_log(i);
    var d = hex_md5(n);
    i.sign = d;
    let a = `${P8_QuickGame_Data.data_url}/log/level`;
    console.log("升级上报请求服务器的参数: " + advancedObjectToString(i));
    let o = a + "?" + keyValueConnect(i);
    console.log("升级上报请求Url: ", o);
    qgRequest(a, "GET", i, (e) => {
      let t = dateOrRes(e);
      console.log("升级数据上报返回的数据 是什么 " + advancedObjectToString(t));
      if (t.result == 0) {
        var res = {
          result: "0",
          data: {
            errorcode: 200,
            msg: "升级数据上报成功"
          }
        };
      } else {
        var res = {
          result: "1",
          data: {
            errorcode: t.errorcode,
            msg: t.msg
          }
        };
      }
      r(res);
      debounce(P8QuickGameSDK.dsjAotLog, 1000)(i, "role_upgrade");
    });
  });
  return e;
};

// 广告自动上报
P8QuickGameSDK.wxVideoAutoLog = function (arg) {
  console.log(" =============== 开始微信广告自动上报逻辑: 数据是 " + JSON.stringify(arg));
  let e = new Promise((i, e) => {
    let t = parseInt(new Date().getTime() / 1e3);
    let data = {
      site: P8_QuickGame_Data.site,
      aid: P8_QuickGame_Data.aid,
      time: t,
      device_type: P8_QuickGame_Data.platform,

      sid: arg.sid || P8_QuickGame_Data.sid || "1",
      uid: P8_QuickGame_Data.uid || "117287480",
      device: P8_QuickGame_Data.device,
      roleid: arg.roleid || P8_QuickGame_Data.roleid || P8_QuickGame_Data.uid || "1",
      rolename: arg.rolename || P8_QuickGame_Data.rolename || P8_QuickGame_Data.uid || "1",
      level: arg.level || P8_QuickGame_Data.level || "1",
      game_type: 'mini',
      ad_slot: arg.ad_slot || P8_QuickGame_Data.ad_slot || "激励视频", // 广告位创建的名称 在微信后台申请的广告位的名称
      ad_unit_id: arg.ad_unit_id || P8_QuickGame_Data.ad_unit_id || "1", //广告位id
      type: arg.type, // 'BannerAd' 横幅 'RewardedVideoAd' 激励视频 'InterstitialAd' 插屏广告 'CustomAd' 模板广告
      status: arg.status, // 点击传入 0 观看成功传入 1 banner广告点击就算成功
      ad_positon: arg.ad_positon || P8_QuickGame_Data.ad_positon || "", // 广告展示位置
    };
    ChangeUndefined(data);
    let n = newSignGetType_log(data);
    var r = hex_md5(n);
    data.sign = r;
    console.log('[ 微信广告自动上报请求服务器参数 ] >', advancedObjectToString(data))
    let url = `${P8_QuickGame_Data.data_url}/log/wxRewardedAd`;
    let rUrl = url + "?" + keyValueConnect(data);
    console.log("微信广告自动上报请求Url: ", rUrl)
    qgRequest(url, "GET", data, (e) => {
      console.log("微信广告自动上报返回的数据 是什么 " + advancedObjectToString(e));
      let t = dateOrRes(e);
      if (t.result == 0) {
        var res = {
          result: "0",
          data: {
            errorcode: 200,
            msg: "微信广告上报成功"
          }
        };
      } else {
        var res = {
          result: "1",
          data: {
            errorcode: t.errorcode,
            msg: t.msg
          }
        };
      }
      i(res);
      P8QuickGameSDK.wxVideoLogNew(data);
    });
  });
  return e;
};

P8QuickGameSDK.dsjAotLog = function (params, event_name) {

  let inData = { ...params, ...publicParameters() };

  let t = parseInt(new Date().getTime() / 1e3);

  let logData = {
    event_name: event_name,
    event_time: t,
    data: inData,
    sdk_version: P8QuickGameSDK_VERSION,
    sdk_name: `P8-聚合快应用SDK-${P8_QuickGame_Data.platform}快游戏-p8sdk-quickGame-${P8QuickGameSDK_VERSION}`
  }

  let url = `${P8_QuickGame_Data.dsj_url}/sdk/upload`;
  console.log(` 大数据上报${event_name}请求服务器参数: ` + advancedObjectToString(logData));
  qgRequest(url, "POST", logData, (e) => {
    let response = dateOrRes(e);
    console.log(` 大数据上报${event_name}返回的数据 res： ` + advancedObjectToString(response));
  });
}

P8QuickGameSDK.wxVideoLogNew = function (arg = "{}") {
  let t = parseInt(new Date().getTime() / 1e3);

  // 添加时间戳检查
  if (t === lastVideoReportTime) {
    console.log("防止重复调用：当前时间戳与上次相同");
    return;
  }
  lastVideoReportTime = t; // 更新最后上报时间

  let nowTime = new Date().getTime();
  let endTime = nowTime - ad_show_time;
  let data = {
    site: P8_QuickGame_Data.site,
    aid: P8_QuickGame_Data.aid,
    sid: arg.sid,
    uid: P8_QuickGame_Data.uid,
    device_type: P8_QuickGame_Data.platform,
    device: P8_QuickGame_Data.device,
    ip: P8_QuickGame_Data.ip,
    roleid: arg.roleid,
    rolename: arg.rolename,
    level: arg.level,
    game_type: 'mini',
    ad_slot: arg.ad_slot || P8_QuickGame_Data.ad_slot || "激励视频", // 广告位创建的名称 在微信后台申请的广告位的名称
    ad_unit_id: arg.ad_unit_id || P8_QuickGame_Data.ad_unit_id || "1", //广告位id
    ad_status: arg.status, // 点击传入 0 观看成功传入 1 banner广告点击就算成功
    ad_type: arg.type, // 'BannerAd' 横幅 'RewardedVideoAd' 激励视频 'InterstitialAd' 插屏广告 'CustomAd' 模板广告
    ad_positon: arg.ad_positon || P8_QuickGame_Data.ad_positon || "", // 广告展示位置
    username: arg.username || P8_QuickGame_Data.account,
    device_model: P8_QuickGame_Data.device_model,
    ad_show_time: arg.status == 0 ? 0 : Math.floor((endTime) / 1000),
    vip: arg.vip,
    ad_bid: "1",
    media_params: start_param,
    device_code: P8_QuickGame_Data.device,
    game_id: P8_QuickGame_Data.game_id,
    is_model: 1,
    sdk_version: P8QuickGameSDK_VERSION,
    sdk_name: `P8-聚合快应用SDK-${P8_QuickGame_Data.platform}快游戏-p8sdk-quickGame-${P8QuickGameSDK_VERSION}`
  };
  ChangeUndefined(data);
  let wxVideoLog = {
    event_name: "ad_show",
    event_time: t,
    data,
    sdk_version: P8QuickGameSDK_VERSION,
    sdk_name: `P8-聚合快应用SDK-${P8_QuickGame_Data.platform}快游戏-p8sdk-quickGame-${P8QuickGameSDK_VERSION}`
  };
  let d = `${P8_QuickGame_Data.dsj_url}/sdk/upload`;
  console.log(" 大数据广告上报请求服务器参数: " + advancedObjectToString(wxVideoLog));
  qgRequest(d, "POST", wxVideoLog, (e) => {
    let data = dateOrRes(e);
    console.log(" 大数据广告返回的数据 res： " + advancedObjectToString(data));
  });
}

function publicParameters() {
  let params = {
    site: P8_QuickGame_Data.site,
    aid: P8_QuickGame_Data.aid,
    uid: P8_QuickGame_Data.uid,
    device: P8_QuickGame_Data.device,
    device_type: P8_QuickGame_Data.modeltype,
    username: P8_QuickGame_Data.account,
    ip: P8_QuickGame_Data.ip,
    mac: P8_QuickGame_Data.mac,
    gameversion: P8_QuickGame_Data.gameversion,
    device_model: P8_QuickGame_Data.device_model,
    device_resolution: P8_QuickGame_Data.device_resolution,
    device_version: P8_QuickGame_Data.device_version,
    device_net: P8_QuickGame_Data.device_net,
    game_type: 'mini',
    media_params: start_param,
    device_code: P8_QuickGame_Data.device,
    game_id: P8_QuickGame_Data.game_id,
    is_model: 1,
    sdk_version: P8QuickGameSDK_VERSION,
    sdk_name: `P8-聚合快应用SDK-${P8_QuickGame_Data.platform}快游戏-p8sdk-quickGame-${P8QuickGameSDK_VERSION}`,
    source_type: 3,
    source_from: ""
  }
  return params;
}

function setheartbeat(e) {
  console.log('[ 心跳时间上报开始 ] >', advancedObjectToString(e))
  let t = setInterval(() => {
    console.log("计时器");
    heartbeat(e);
  }, 3e5);
  console.log("time =", t);
}

function heartbeat(e) {
  let t = parseInt(new Date().getTime() / 1e3);
  console.log("***t", t);
  let i = {
    aid: e.aid,
    uid: e.uid,
    site: e.site,
    device: e.device,
    modeltype: e.modeltype,
    username: e.username,
    sid: e.sid,
    roleid: e.roleid,
    rolename: e.rolename,
    level: e.level,
    vip: e.vip,
    ip: e.ip,
    onlinetime: e.onlinetime,
    device_model: e.device_model,
    device_resolution: e.device_resolution,
    device_version: e.device_version,
    device_net: e.device_net,
    time: t,
    oaid: e.oaid
  };

  ChangeUndefined(i);
  let r = newSignGetType_log(i);
  var n = hex_md5(r);
  i.sign = n;
  console.log('[ 心跳时间上报请求服务器参数 ] >', advancedObjectToString(i))
  let d = `${P8_QuickGame_Data.data_url}/log/onlineTime`;
  let rUrl = d + "?" + keyValueConnect(i);
  console.log('[ 心跳时间上报请求Url ] >', rUrl)
  qgRequest(d, "GET", i, (e) => {
    var t = dateOrRes(e);
    console.log("心跳时间上报= ", advancedObjectToString(t));
  });
}
// ========================================================== 公共行为上报方法end =================================================

// ========================================================== unit方法start ===============================================

P8QuickGameSDK.print = function (...args) {
  // 基本日志前缀
  const prefix = '[聚合SDK] 日志';

  // 检查是否包含复杂对象
  const hasComplexObjects = args.some(arg =>
    arg !== null && typeof arg === 'object'
  );

  if (hasComplexObjects) {
    // 打印基本日志
    console.log(prefix);
    // 逐个打印参数，对对象使用更详细的输出
    args.forEach((arg, index) => {
      if (arg !== null && typeof arg === 'object') {
        console.log(`参数${index + 1}:`, JSON.stringify(arg, null, 2));
      } else {
        console.log(`参数${index + 1}:`, arg);
      }
    });
  } else {
    // 简单值直接使用标准打印
    console.log(prefix, ...args);
  }
}

function qgRequest(e, a = "post", t = {}, n = null, f = null, i = true) {
  if (qg.request) {
    qg.request({
      url: e,
      method: a,
      data: t,
      dataType: "json",
      success: (success) => {
        if (n) {
          n(success);
        }
      },
      fail: (error) => {
        if (f) {
          f(error);
        }
      },
    });
  } else {
    let r = new XMLHttpRequest;
    let url = e + "?" + keyValueConnect(t);
    r.open(a, url);
    if (i) {
      r.setRequestHeader("Content-type", "application/json")
    } else {
      console.log("术良 url" + url);
      r.setRequestHeader("Content-type", "application/x-www-form-urlencoded")
    }
    r.send(JSON.stringify(t));
    r.onreadystatechange = function () {
      if (r.status == 200 && r.readyState == 4) {
        let e = JSON.parse(r.responseText);
        console.log("请求返回的数据是什么:" + JSON.stringify(e));
        if (n) {
          n(e)
        }
      }
    }
  }
}

function P8Log(e) {
  console.log(" ======== [聚合SDK] 日志 ========>" + e)
}

function print() {
  let t = "";
  for (let e = 0; e < 20; e++) {
    if (!arguments[e]) {
      console.log(t);
      return;
    }
    t += " " + JSON.stringify(arguments[e]);
  }
}

function advancedObjectToString(obj, depth = 0, maxDepth = 10) {
  // 处理基础类型和特殊情况
  if (obj === null) return 'null';
  if (obj === undefined) return 'undefined';
  if (typeof obj === 'function') return '[Function]';
  if (typeof obj !== 'object') return String(obj);
  if (depth > maxDepth) return '[Object]'; // 防止过深递归

  // 处理数组
  if (Array.isArray(obj)) {
    const items = obj.map(item => advancedObjectToString(item, depth + 1, maxDepth));
    return `[${items.join(', ')}]`;
  }

  // 处理普通对象
  const indent = '  '.repeat(depth);
  const pairs = [];

  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      try {
        const value = obj[key];
        pairs.push(`${key}: ${advancedObjectToString(value, depth + 1, maxDepth)}`);
      } catch (e) {
        pairs.push(`${key}: [Error: ${e.message}]`);
      }
    }
  }

  return `{\n${indent}  ${pairs.join(',\n' + indent + '  ')}\n${indent}}`;
}

function getNonce() {
  let s = "";
  for (let i = 0; i < 32; i++) {
    let tmp = Math.floor(Math.random() * 10);
    s = s + tmp.toString();
  }
  return s;
}

function dateFormat() {
  let date = new Date();
  let ret;
  let fmt = "yyyyMMddHHmmss";
  const opt = {
    "y+": date.getFullYear().toString(), // 年
    "M+": (date.getMonth() + 1).toString(), // 月
    "d+": date.getDate().toString(), // 日
    "H+": date.getHours().toString(), // 时
    "m+": date.getMinutes().toString(), // 分
    "s+": date.getSeconds().toString() // 秒
    // 有其他格式化字符需求可以继续添加，必须转化成字符串
  };
  let mm = date.getMinutes();
  let HH = date.getHours();
  if (mm + 10 > 60) {
    mm = mm - 50;
    HH = HH + 1;
  } else {
    mm = mm + 10;
  }
  opt["m+"] = mm.toString()
  opt["H+"] = HH.toString()
  for (let k in opt) {
    ret = new RegExp("(" + k + ")").exec(fmt);
    if (ret) {
      fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
    };
  };
  return fmt;
}

function ChangeUndefined(t) {
  for (let e in t) {
    if (t.hasOwnProperty(e)) {
      if (typeof t[e] == "undefined") {
        t[e] = "";
      }
    }
  }
}

function newSignGetType_log(t) {
  var i = [];
  for (var e in t) {
    i.push(e);
  }
  i = i.sort();
  let r = P8_QuickGame_Data.site;
  for (let e = 0; e < i.length; e++) {
    const n = t[i[e]];
    if (e != 0) {
      r += "&" + i[e] + "=" + n;
    } else {
      r += i[e] + "=" + n;
    }
  }
  r += P8_QuickGame_Data.key;
  return r;
}

function dateOrRes(e) {
  return e.data ? e.data : e;
}

function keyValueConnect(e) {
  let a = "";
  for (const t in e) {
    if (e.hasOwnProperty.call(e, t)) {
      const n = e[t];
      a += t + "=" + n + "&"
    }
  }
  a = a.substring(0, a.length - 1);
  return a
}

function getTime() {
  var e = new Date;
  let a = e.getFullYear();
  let t = e.getMonth() + 1;
  let n = e.getDate();
  let i = e.getHours();
  let r = e.getMinutes();
  let o = e.getSeconds();
  return a + "/" + t + "/" + n + " " + i + ":" + r + ":" + o
}

function debounce(fn, delay = 1000) {
  let timer = null;
  return function (...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  }
}

function destroyVideoAd() {
  if (qg_videoAD) {
    // 移除所有事件监听
    qg_videoAD.offLoad();
    qg_videoAD.offError();
    qg_videoAD.offClose();

    // 销毁实例
    if (typeof qg_videoAD.destroy === 'function') {
      qg_videoAD.destroy();
    }

    qg_videoAD = null;
  }
  // 重置状态
  adLoadState.isLoading = false;
  adLoadState.isReady = false;
}

/**
 * 通用HTTP请求封装函数
 * @param {Object} options 请求选项
 * @param {string} options.url 请求URL
 * @param {string} [options.method='GET'] 请求方法(GET, POST, PUT, DELETE等)
 * @param {Object} [options.data={}] 请求数据
 * @param {Object} [options.headers={}] 请求头
 * @param {boolean} [options.useJSON=true] 是否使用JSON格式
 * @param {number} [options.timeout=30000] 超时时间(毫秒)
 * @param {boolean} [options.usePlatformAPI=true] 是否优先使用平台API(如qg.request)
 * @returns {Promise<any>} 返回Promise对象
 */
function httpRequest(options) {
  // 参数默认值和校验
  const url = options.url;
  if (!url) {
    return Promise.reject(new Error('URL不能为空'));
  }

  const method = (options.method || 'GET').toUpperCase();
  const data = options.data || {};
  const headers = options.headers || {};
  const useJSON = options.useJSON !== false;
  const timeout = options.timeout || 30000;
  const usePlatformAPI = options.usePlatformAPI !== false;

  // 返回Promise
  return new Promise((resolve, reject) => {
    // 构建请求参数
    const requestParams = {
      url: url,
      method: method,
      data: data,
      dataType: 'json',
      success: (res) => {
        resolve(res);
      },
      fail: (error) => {
        reject(error);
      }
    };

    // 优先使用平台API
    if (usePlatformAPI && typeof qg !== 'undefined' && qg.request) {
      // 如果有指定headers，添加到请求中
      if (headers && Object.keys(headers).length > 0) {
        requestParams.header = headers;
      }

      // 设置超时
      if (timeout) {
        requestParams.timeout = timeout;
      }

      // 发起请求
      qg.request(requestParams);
    } else {
      // 回退到XMLHttpRequest
      console.log('[ 回退到XMLHttpRequest ] >', advancedObjectToString(requestParams))
      try {
        const xhr = new XMLHttpRequest();
        let urlWithParams = url;

        // 处理GET请求参数
        if (method === 'GET' && data && Object.keys(data).length > 0) {
          const params = [];
          for (const key in data) {
            if (data.hasOwnProperty(key)) {
              params.push(`${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`);
            }
          }
          urlWithParams = `${url}${url.includes('?') ? '&' : '?'}${params.join('&')}`;
        }

        // 打开连接
        xhr.open(method, urlWithParams, true);

        // 设置超时
        if (timeout) {
          xhr.timeout = timeout;
        }

        // 设置请求头
        if (useJSON && method !== 'GET') {
          xhr.setRequestHeader('Content-Type', 'application/json');
        } else if (method !== 'GET') {
          xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        }

        // 添加自定义请求头
        if (headers) {
          for (const key in headers) {
            if (headers.hasOwnProperty(key)) {
              xhr.setRequestHeader(key, headers[key]);
            }
          }
        }

        // 注册事件回调
        xhr.onreadystatechange = function () {
          if (xhr.readyState === 4) {
            if (xhr.status >= 200 && xhr.status < 300) {
              try {
                const response = JSON.parse(xhr.responseText);
                resolve(response);
              } catch (e) {
                // 非JSON响应
                resolve(xhr.responseText);
              }
            } else {
              reject({
                status: xhr.status,
                statusText: xhr.statusText,
                response: xhr.responseText
              });
            }
          }
        };

        xhr.onerror = function () {
          reject({
            status: xhr.status,
            statusText: '请求失败',
            error: new Error('Network Error')
          });
        };

        xhr.ontimeout = function () {
          reject({
            status: 408,
            statusText: '请求超时',
            error: new Error('Request timeout')
          });
        };

        // 发送请求
        if (method === 'GET') {
          xhr.send();
        } else {
          xhr.send(useJSON ? JSON.stringify(data) : httpkeyValueConnect(data));
        }
      } catch (error) {
        reject({
          status: 0,
          statusText: '请求异常',
          error: error
        });
      }
    }
  });
}

/**
 * 将对象转换为key=value&key=value格式字符串
 * @param {Object} data 要转换的对象
 * @returns {string} 表单格式字符串
 */
function httpkeyValueConnect(data) {
  if (!data || typeof data !== 'object') {
    return '';
  }

  let result = '';
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      const value = data[key];
      result += `${key}=${value}&`;
    }
  }

  return result.length > 0 ? result.substring(0, result.length - 1) : '';
}

// ========================================================== unit方法end =================================================

// ========================================================== md5方法start =================================================

var V = V || {};
V.Security = V.Security || {};
(function () {
  // for faster access
  var S = V.Security;
  /**
   * The highest integer value a number can go to without losing precision.
   */
  S.maxExactInt = Math.pow(2, 53);
  /**
   * Converts string from internal UTF-16 to UTF-8
   * and saves it using array of numbers (bytes), 0-255 per cell
   * @param {String} str
   * @return {Array}
   */
  S.toUtf8ByteArr = function (str) {
    var arr = [],
      code;
    for (var i = 0; i < str.length; i++) {
      code = str.charCodeAt(i);
      /*
                  Note that charCodeAt will always return a value that is less than 65,536.
                  This is because the higher code points are represented by a pair of (lower valued)
                  "surrogate" pseudo-characters which are used to comprise the real character.
                  Because of this, in order to examine or reproduce the full character for
                  individual characters of value 65,536 and above, for such characters,
                  it is necessary to retrieve not only charCodeAt(0), but also charCodeAt(1). 
                   */
      if (0xd800 <= code && code <= 0xdbff) {
        // UTF-16 high surrogate
        var hi = code,
          low = str.charCodeAt(i + 1);
        code = (hi - 0xd800) * 0x400 + (low - 0xdc00) + 0x10000;
        i++;
      }
      if (code <= 127) {
        arr[arr.length] = code;
      } else if (code <= 2047) {
        arr[arr.length] = (code >>> 6) + 0xc0;
        arr[arr.length] = (code & 0x3f) | 0x80;
      } else if (code <= 65535) {
        arr[arr.length] = (code >>> 12) + 0xe0;
        arr[arr.length] = ((code >>> 6) & 0x3f) | 0x80;
        arr[arr.length] = (code & 0x3f) | 0x80;
      } else if (code <= 1114111) {
        arr[arr.length] = (code >>> 18) + 0xf0;
        arr[arr.length] = ((code >>> 12) & 0x3f) | 0x80;
        arr[arr.length] = ((code >>> 6) & 0x3f) | 0x80;
        arr[arr.length] = (code & 0x3f) | 0x80;
      } else {
        throw "Unicode standart supports code points up-to U+10FFFF";
      }
    }
    return arr;
  };
  /**
   * Outputs 32 integer bits of a number in hex format.
   * Preserves leading zeros.
   * @param {Number} num
   */
  S.toHex32 = function (num) {
    // if negative
    if (num & 0x80000000) {
      // convert to positive number
      num = num & ~0x80000000;
      num += Math.pow(2, 31);
    }
    var str = num.toString(16);
    while (str.length < 8) {
      str = "0" + str;
    }
    return str;
  };
  /**
   * Changes the order of 4 bytes in integer representation of number.
   * From 1234 to 4321.
   * @param {Number} num Only 32 int bits are used.
   */
  S.reverseBytes = function (num) {
    var res = 0;
    res += (num >>> 24) & 0xff;
    res += ((num >>> 16) & 0xff) << 8;
    res += ((num >>> 8) & 0xff) << 16;
    res += (num & 0xff) << 24;
    return res;
  };
  S.leftRotate = function (x, c) {
    return (x << c) | (x >>> (32 - c));
  };
  /**
   * RSA Data Security, Inc. MD5 Message-Digest Algorithm
   * http://tools.ietf.org/html/rfc1321
   * http://en.wikipedia.org/wiki/MD5
   * @param {String} message
   */
  S.md5 = function (message) {
    // r specifies the per-round shift amounts
    var r = [
      7, 12, 17, 22, 7, 12, 17, 22, 7, 12, 17, 22, 7, 12, 17, 22, 5, 9, 14, 20, 5, 9, 14, 20, 5, 9, 14, 20, 5, 9, 14, 20, 4, 11, 16, 23, 4, 11, 16, 23, 4, 11, 16, 23, 4, 11, 16, 23, 6, 10, 15, 21, 6,
      10, 15, 21, 6, 10, 15, 21, 6, 10, 15, 21
    ];
    // Use binary integer part of the sines of integers (Radians) as constants:
    var k = [];
    for (var i = 0; i <= 63; i++) {
      k[i] = (Math.abs(Math.sin(i + 1)) * Math.pow(2, 32)) << 0;
    }
    var h0 = 0x67452301,
      h1 = 0xefcdab89,
      h2 = 0x98badcfe,
      h3 = 0x10325476,
      bytes,
      unpadded;
    //Pre-processing:
    bytes = S.toUtf8ByteArr(message);
    message = null;
    unpadded = bytes.length;
    //append "1" bit to message
    //append "0" bits until message length in bits ≡ 448 (mod 512)
    bytes.push(0x80);
    var zeroBytes = Math.abs(448 - ((bytes.length * 8) % 512)) / 8;
    while (zeroBytes--) {
      bytes.push(0);
    }
    //append bit length of unpadded message as 64-bit little-endian integer to message
    bytes.push((unpadded * 8) & 0xff, ((unpadded * 8) >> 8) & 0xff, ((unpadded * 8) >> 16) & 0xff, ((unpadded * 8) >> 24) & 0xff);
    var i = 4;
    while (i--) {
      bytes.push(0);
    }
    var leftRotate = S.leftRotate;
    //Process the message in successive 512-bit chunks:
    var i = 0,
      w = [];
    while (i < bytes.length) {
      //break chunk into sixteen 32-bit words w[i], 0 ≤ i ≤ 15
      for (var j = 0; j <= 15; j++) {
        w[j] = (bytes[i + 4 * j] << 0) + (bytes[i + 4 * j + 1] << 8) + (bytes[i + 4 * j + 2] << 16) + (bytes[i + 4 * j + 3] << 24);
      }
      //Initialize hash value for this chunk:
      var a = h0,
        b = h1,
        c = h2,
        d = h3,
        f,
        g;
      //Main loop:
      for (var j = 0; j <= 63; j++) {
        if (j <= 15) {
          f = (b & c) | (~b & d);
          g = j;
        } else if (j <= 31) {
          f = (d & b) | (~d & c);
          g = (5 * j + 1) % 16;
        } else if (j <= 47) {
          f = b ^ c ^ d;
          g = (3 * j + 5) % 16;
        } else {
          f = c ^ (b | ~d);
          g = (7 * j) % 16;
        }
        var temp = d;
        d = c;
        c = b;
        b = b + leftRotate(a + f + k[j] + w[g], r[j]);
        a = temp;
      }
      //Add this chunk's hash to result so far:
      h0 = (h0 + a) << 0;
      h1 = (h1 + b) << 0;
      h2 = (h2 + c) << 0;
      h3 = (h3 + d) << 0;
      i += 512 / 8;
    }
    // fix when starting with 0
    var res = out(h0) + out(h1) + out(h2) + out(h3);

    function out(h) {
      return S.toHex32(S.reverseBytes(h));
    }
    return res;
  };
})();

var hexcase = 0; /* hex output format. 0 - lowercase; 1 - uppercase        */
var b64pad = ""; /* base-64 pad character. "=" for strict RFC compliance   */

function hex_md5(s) {
  return rstr2hex(rstr_md5(str2rstr_utf8(s)));
}

function rstr_md5(s) {
  return binl2rstr(binl_md5(rstr2binl(s), s.length * 8));
}

function rstr2hex(input) {
  try {
    hexcase;
  } catch (e) {
    hexcase = 0;
  }
  var hex_tab = hexcase ? "0123456789ABCDEF" : "0123456789abcdef";
  var output = "";
  var x;
  for (var i = 0; i < input.length; i++) {
    x = input.charCodeAt(i);
    output += hex_tab.charAt((x >>> 4) & 0x0f) + hex_tab.charAt(x & 0x0f);
  }
  return output;
}

function str2rstr_utf8(input) {
  return unescape(encodeURI(input));
}

function rstr2binl(input) {
  var output = Array(input.length >> 2);
  for (var i = 0; i < output.length; i++) output[i] = 0;
  for (var i = 0; i < input.length * 8; i += 8) output[i >> 5] |= (input.charCodeAt(i / 8) & 0xff) << i % 32;
  return output;
}

function binl2rstr(input) {
  var output = "";
  for (var i = 0; i < input.length * 32; i += 8) output += String.fromCharCode((input[i >> 5] >>> i % 32) & 0xff);
  return output;
}

function binl_md5(x, len) {
  /* append padding */
  x[len >> 5] |= 0x80 << len % 32;
  x[(((len + 64) >>> 9) << 4) + 14] = len;
  var a = 1732584193;
  var b = -271733879;
  var c = -1732584194;
  var d = 271733878;
  for (var i = 0; i < x.length; i += 16) {
    var olda = a;
    var oldb = b;
    var oldc = c;
    var oldd = d;
    a = md5_ff(a, b, c, d, x[i + 0], 7, -680876936);
    d = md5_ff(d, a, b, c, x[i + 1], 12, -389564586);
    c = md5_ff(c, d, a, b, x[i + 2], 17, 606105819);
    b = md5_ff(b, c, d, a, x[i + 3], 22, -1044525330);
    a = md5_ff(a, b, c, d, x[i + 4], 7, -176418897);
    d = md5_ff(d, a, b, c, x[i + 5], 12, 1200080426);
    c = md5_ff(c, d, a, b, x[i + 6], 17, -1473231341);
    b = md5_ff(b, c, d, a, x[i + 7], 22, -45705983);
    a = md5_ff(a, b, c, d, x[i + 8], 7, 1770035416);
    d = md5_ff(d, a, b, c, x[i + 9], 12, -1958414417);
    c = md5_ff(c, d, a, b, x[i + 10], 17, -42063);
    b = md5_ff(b, c, d, a, x[i + 11], 22, -1990404162);
    a = md5_ff(a, b, c, d, x[i + 12], 7, 1804603682);
    d = md5_ff(d, a, b, c, x[i + 13], 12, -40341101);
    c = md5_ff(c, d, a, b, x[i + 14], 17, -1502002290);
    b = md5_ff(b, c, d, a, x[i + 15], 22, 1236535329);
    a = md5_gg(a, b, c, d, x[i + 1], 5, -165796510);
    d = md5_gg(d, a, b, c, x[i + 6], 9, -1069501632);
    c = md5_gg(c, d, a, b, x[i + 11], 14, 643717713);
    b = md5_gg(b, c, d, a, x[i + 0], 20, -373897302);
    a = md5_gg(a, b, c, d, x[i + 5], 5, -701558691);
    d = md5_gg(d, a, b, c, x[i + 10], 9, 38016083);
    c = md5_gg(c, d, a, b, x[i + 15], 14, -660478335);
    b = md5_gg(b, c, d, a, x[i + 4], 20, -405537848);
    a = md5_gg(a, b, c, d, x[i + 9], 5, 568446438);
    d = md5_gg(d, a, b, c, x[i + 14], 9, -1019803690);
    c = md5_gg(c, d, a, b, x[i + 3], 14, -187363961);
    b = md5_gg(b, c, d, a, x[i + 8], 20, 1163531501);
    a = md5_gg(a, b, c, d, x[i + 13], 5, -1444681467);
    d = md5_gg(d, a, b, c, x[i + 2], 9, -51403784);
    c = md5_gg(c, d, a, b, x[i + 7], 14, 1735328473);
    b = md5_gg(b, c, d, a, x[i + 12], 20, -1926607734);
    a = md5_hh(a, b, c, d, x[i + 5], 4, -378558);
    d = md5_hh(d, a, b, c, x[i + 8], 11, -2022574463);
    c = md5_hh(c, d, a, b, x[i + 11], 16, 1839030562);
    b = md5_hh(b, c, d, a, x[i + 14], 23, -35309556);
    a = md5_hh(a, b, c, d, x[i + 1], 4, -1530992060);
    d = md5_hh(d, a, b, c, x[i + 4], 11, 1272893353);
    c = md5_hh(c, d, a, b, x[i + 7], 16, -155497632);
    b = md5_hh(b, c, d, a, x[i + 10], 23, -1094730640);
    a = md5_hh(a, b, c, d, x[i + 13], 4, 681279174);
    d = md5_hh(d, a, b, c, x[i + 0], 11, -358537222);
    c = md5_hh(c, d, a, b, x[i + 3], 16, -722521979);
    b = md5_hh(b, c, d, a, x[i + 6], 23, 76029189);
    a = md5_hh(a, b, c, d, x[i + 9], 4, -640364487);
    d = md5_hh(d, a, b, c, x[i + 12], 11, -421815835);
    c = md5_hh(c, d, a, b, x[i + 15], 16, 530742520);
    b = md5_hh(b, c, d, a, x[i + 2], 23, -995338651);
    a = md5_ii(a, b, c, d, x[i + 0], 6, -198630844);
    d = md5_ii(d, a, b, c, x[i + 7], 10, 1126891415);
    c = md5_ii(c, d, a, b, x[i + 14], 15, -1416354905);
    b = md5_ii(b, c, d, a, x[i + 5], 21, -57434055);
    a = md5_ii(a, b, c, d, x[i + 12], 6, 1700485571);
    d = md5_ii(d, a, b, c, x[i + 3], 10, -1894986606);
    c = md5_ii(c, d, a, b, x[i + 10], 15, -1051523);
    b = md5_ii(b, c, d, a, x[i + 1], 21, -2054922799);
    a = md5_ii(a, b, c, d, x[i + 8], 6, 1873313359);
    d = md5_ii(d, a, b, c, x[i + 15], 10, -30611744);
    c = md5_ii(c, d, a, b, x[i + 6], 15, -1560198380);
    b = md5_ii(b, c, d, a, x[i + 13], 21, 1309151649);
    a = md5_ii(a, b, c, d, x[i + 4], 6, -145523070);
    d = md5_ii(d, a, b, c, x[i + 11], 10, -1120210379);
    c = md5_ii(c, d, a, b, x[i + 2], 15, 718787259);
    b = md5_ii(b, c, d, a, x[i + 9], 21, -343485551);
    a = safe_add(a, olda);
    b = safe_add(b, oldb);
    c = safe_add(c, oldc);
    d = safe_add(d, oldd);
  }
  return Array(a, b, c, d);
}

function md5_cmn(q, a, b, x, s, t) {
  return safe_add(bit_rol(safe_add(safe_add(a, q), safe_add(x, t)), s), b);
}

function md5_ff(a, b, c, d, x, s, t) {
  return md5_cmn((b & c) | (~b & d), a, b, x, s, t);
}

function md5_gg(a, b, c, d, x, s, t) {
  return md5_cmn((b & d) | (c & ~d), a, b, x, s, t);
}

function md5_hh(a, b, c, d, x, s, t) {
  return md5_cmn(b ^ c ^ d, a, b, x, s, t);
}

function md5_ii(a, b, c, d, x, s, t) {
  return md5_cmn(c ^ (b | ~d), a, b, x, s, t);
}

function safe_add(x, y) {
  var lsw = (x & 0xffff) + (y & 0xffff);
  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);
  return (msw << 16) | (lsw & 0xffff);
}

function bit_rol(num, cnt) {
  return (num << cnt) | (num >>> (32 - cnt));
}

function md5cycle(x, k) {
  var a = x[0],
    b = x[1],
    c = x[2],
    d = x[3];
  a = ff(a, b, c, d, k[0], 7, -680876936);
  d = ff(d, a, b, c, k[1], 12, -389564586);
  c = ff(c, d, a, b, k[2], 17, 606105819);
  b = ff(b, c, d, a, k[3], 22, -1044525330);
  a = ff(a, b, c, d, k[4], 7, -176418897);
  d = ff(d, a, b, c, k[5], 12, 1200080426);
  c = ff(c, d, a, b, k[6], 17, -1473231341);
  b = ff(b, c, d, a, k[7], 22, -45705983);
  a = ff(a, b, c, d, k[8], 7, 1770035416);
  d = ff(d, a, b, c, k[9], 12, -1958414417);
  c = ff(c, d, a, b, k[10], 17, -42063);
  b = ff(b, c, d, a, k[11], 22, -1990404162);
  a = ff(a, b, c, d, k[12], 7, 1804603682);
  d = ff(d, a, b, c, k[13], 12, -40341101);
  c = ff(c, d, a, b, k[14], 17, -1502002290);
  b = ff(b, c, d, a, k[15], 22, 1236535329);
  a = gg(a, b, c, d, k[1], 5, -165796510);
  d = gg(d, a, b, c, k[6], 9, -1069501632);
  c = gg(c, d, a, b, k[11], 14, 643717713);
  b = gg(b, c, d, a, k[0], 20, -373897302);
  a = gg(a, b, c, d, k[5], 5, -701558691);
  d = gg(d, a, b, c, k[10], 9, 38016083);
  c = gg(c, d, a, b, k[15], 14, -660478335);
  b = gg(b, c, d, a, k[4], 20, -405537848);
  a = gg(a, b, c, d, k[9], 5, 568446438);
  d = gg(d, a, b, c, k[14], 9, -1019803690);
  c = gg(c, d, a, b, k[3], 14, -187363961);
  b = gg(b, c, d, a, k[8], 20, 1163531501);
  a = gg(a, b, c, d, k[13], 5, -1444681467);
  d = gg(d, a, b, c, k[2], 9, -51403784);
  c = gg(c, d, a, b, k[7], 14, 1735328473);
  b = gg(b, c, d, a, k[12], 20, -1926607734);
  a = hh(a, b, c, d, k[5], 4, -378558);
  d = hh(d, a, b, c, k[8], 11, -2022574463);
  c = hh(c, d, a, b, k[11], 16, 1839030562);
  b = hh(b, c, d, a, k[14], 23, -35309556);
  a = hh(a, b, c, d, k[1], 4, -1530992060);
  d = hh(d, a, b, c, k[4], 11, 1272893353);
  c = hh(c, d, a, b, k[7], 16, -155497632);
  b = hh(b, c, d, a, k[10], 23, -1094730640);
  a = hh(a, b, c, d, k[13], 4, 681279174);
  d = hh(d, a, b, c, k[0], 11, -358537222);
  c = hh(c, d, a, b, k[3], 16, -722521979);
  b = hh(b, c, d, a, k[6], 23, 76029189);
  a = hh(a, b, c, d, k[9], 4, -640364487);
  d = hh(d, a, b, c, k[12], 11, -421815835);
  c = hh(c, d, a, b, k[15], 16, 530742520);
  b = hh(b, c, d, a, k[2], 23, -995338651);
  a = ii(a, b, c, d, k[0], 6, -198630844);
  d = ii(d, a, b, c, k[7], 10, 1126891415);
  c = ii(c, d, a, b, k[14], 15, -1416354905);
  b = ii(b, c, d, a, k[5], 21, -57434055);
  a = ii(a, b, c, d, k[12], 6, 1700485571);
  d = ii(d, a, b, c, k[3], 10, -1894986606);
  c = ii(c, d, a, b, k[10], 15, -1051523);
  b = ii(b, c, d, a, k[1], 21, -2054922799);
  a = ii(a, b, c, d, k[8], 6, 1873313359);
  d = ii(d, a, b, c, k[15], 10, -30611744);
  c = ii(c, d, a, b, k[6], 15, -1560198380);
  b = ii(b, c, d, a, k[13], 21, 1309151649);
  a = ii(a, b, c, d, k[4], 6, -145523070);
  d = ii(d, a, b, c, k[11], 10, -1120210379);
  c = ii(c, d, a, b, k[2], 15, 718787259);
  b = ii(b, c, d, a, k[9], 21, -343485551);
  x[0] = add32(a, x[0]);
  x[1] = add32(b, x[1]);
  x[2] = add32(c, x[2]);
  x[3] = add32(d, x[3]);
}

function cmn(q, a, b, x, s, t) {
  a = add32(add32(a, q), add32(x, t));
  return add32((a << s) | (a >>> (32 - s)), b);
}

function ff(a, b, c, d, x, s, t) {
  return cmn((b & c) | (~b & d), a, b, x, s, t);
}

function gg(a, b, c, d, x, s, t) {
  return cmn((b & d) | (c & ~d), a, b, x, s, t);
}

function hh(a, b, c, d, x, s, t) {
  return cmn(b ^ c ^ d, a, b, x, s, t);
}

function ii(a, b, c, d, x, s, t) {
  return cmn(c ^ (b | ~d), a, b, x, s, t);
}

function md51(s) {
  var txt = "";
  var n = s.length,
    state = [1732584193, -271733879, -1732584194, 271733878],
    i;
  for (i = 64; i <= s.length; i += 64) {
    md5cycle(state, md5blk(s.substring(i - 64, i)));
  }
  s = s.substring(i - 64);
  var tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
  for (i = 0; i < s.length; i++) tail[i >> 2] |= s.charCodeAt(i) << (i % 4 << 3);
  tail[i >> 2] |= 0x80 << (i % 4 << 3);
  if (i > 55) {
    md5cycle(state, tail);
    for (i = 0; i < 16; i++) tail[i] = 0;
  }
  tail[14] = n * 8;
  md5cycle(state, tail);
  return state;
}

function md5blk(s) {
  /* I figured global was faster.   */
  var md5blks = [],
    i; /* Andy King said do it this way. */
  for (i = 0; i < 64; i += 4) {
    md5blks[i >> 2] = s.charCodeAt(i) + (s.charCodeAt(i + 1) << 8) + (s.charCodeAt(i + 2) << 16) + (s.charCodeAt(i + 3) << 24);
  }
  return md5blks;
}
var hex_chr = "0123456789abcdef".split("");

function rhex(n) {
  var s = "",
    j = 0;
  for (; j < 4; j++) s += hex_chr[(n >> (j * 8 + 4)) & 0x0f] + hex_chr[(n >> (j * 8)) & 0x0f];
  return s;
}

function hex(x) {
  for (var i = 0; i < x.length; i++) x[i] = rhex(x[i]);
  return x.join("");
}

function md5(s) {
  return hex(md51(s));
}

function add32(a, b) {
  return (a + b) & 0xffffffff;
}
if (md5("hello") != "5d41402abc4b2a76b9719d911017c592") {
  function add32(x, y) {
    var lsw = (x & 0xffff) + (y & 0xffff),
      msw = (x >> 16) + (y >> 16) + (lsw >> 16);
    return (msw << 16) | (lsw & 0xffff);
  }
}
(function () {
  function md5cycle(x, k) {
    var a = x[0],
      b = x[1],
      c = x[2],
      d = x[3];
    a = ff(a, b, c, d, k[0], 7, -680876936);
    d = ff(d, a, b, c, k[1], 12, -389564586);
    c = ff(c, d, a, b, k[2], 17, 606105819);
    b = ff(b, c, d, a, k[3], 22, -1044525330);
    a = ff(a, b, c, d, k[4], 7, -176418897);
    d = ff(d, a, b, c, k[5], 12, 1200080426);
    c = ff(c, d, a, b, k[6], 17, -1473231341);
    b = ff(b, c, d, a, k[7], 22, -45705983);
    a = ff(a, b, c, d, k[8], 7, 1770035416);
    d = ff(d, a, b, c, k[9], 12, -1958414417);
    c = ff(c, d, a, b, k[10], 17, -42063);
    b = ff(b, c, d, a, k[11], 22, -1990404162);
    a = ff(a, b, c, d, k[12], 7, 1804603682);
    d = ff(d, a, b, c, k[13], 12, -40341101);
    c = ff(c, d, a, b, k[14], 17, -1502002290);
    b = ff(b, c, d, a, k[15], 22, 1236535329);
    a = gg(a, b, c, d, k[1], 5, -165796510);
    d = gg(d, a, b, c, k[6], 9, -1069501632);
    c = gg(c, d, a, b, k[11], 14, 643717713);
    b = gg(b, c, d, a, k[0], 20, -373897302);
    a = gg(a, b, c, d, k[5], 5, -701558691);
    d = gg(d, a, b, c, k[10], 9, 38016083);
    c = gg(c, d, a, b, k[15], 14, -660478335);
    b = gg(b, c, d, a, k[4], 20, -405537848);
    a = gg(a, b, c, d, k[9], 5, 568446438);
    d = gg(d, a, b, c, k[14], 9, -1019803690);
    c = gg(c, d, a, b, k[3], 14, -187363961);
    b = gg(b, c, d, a, k[8], 20, 1163531501);
    a = gg(a, b, c, d, k[13], 5, -1444681467);
    d = gg(d, a, b, c, k[2], 9, -51403784);
    c = gg(c, d, a, b, k[7], 14, 1735328473);
    b = gg(b, c, d, a, k[12], 20, -1926607734);
    a = hh(a, b, c, d, k[5], 4, -378558);
    d = hh(d, a, b, c, k[8], 11, -2022574463);
    c = hh(c, d, a, b, k[11], 16, 1839030562);
    b = hh(b, c, d, a, k[14], 23, -35309556);
    a = hh(a, b, c, d, k[1], 4, -1530992060);
    d = hh(d, a, b, c, k[4], 11, 1272893353);
    c = hh(c, d, a, b, k[7], 16, -155497632);
    b = hh(b, c, d, a, k[10], 23, -1094730640);
    a = hh(a, b, c, d, k[13], 4, 681279174);
    d = hh(d, a, b, c, k[0], 11, -358537222);
    c = hh(c, d, a, b, k[3], 16, -722521979);
    b = hh(b, c, d, a, k[6], 23, 76029189);
    a = hh(a, b, c, d, k[9], 4, -640364487);
    d = hh(d, a, b, c, k[12], 11, -421815835);
    c = hh(c, d, a, b, k[15], 16, 530742520);
    b = hh(b, c, d, a, k[2], 23, -995338651);
    a = ii(a, b, c, d, k[0], 6, -198630844);
    d = ii(d, a, b, c, k[7], 10, 1126891415);
    c = ii(c, d, a, b, k[14], 15, -1416354905);
    b = ii(b, c, d, a, k[5], 21, -57434055);
    a = ii(a, b, c, d, k[12], 6, 1700485571);
    d = ii(d, a, b, c, k[3], 10, -1894986606);
    c = ii(c, d, a, b, k[10], 15, -1051523);
    b = ii(b, c, d, a, k[1], 21, -2054922799);
    a = ii(a, b, c, d, k[8], 6, 1873313359);
    d = ii(d, a, b, c, k[15], 10, -30611744);
    c = ii(c, d, a, b, k[6], 15, -1560198380);
    b = ii(b, c, d, a, k[13], 21, 1309151649);
    a = ii(a, b, c, d, k[4], 6, -145523070);
    d = ii(d, a, b, c, k[11], 10, -1120210379);
    c = ii(c, d, a, b, k[2], 15, 718787259);
    b = ii(b, c, d, a, k[9], 21, -343485551);
    x[0] = add32(a, x[0]);
    x[1] = add32(b, x[1]);
    x[2] = add32(c, x[2]);
    x[3] = add32(d, x[3]);
  }

  function cmn(q, a, b, x, s, t) {
    a = add32(add32(a, q), add32(x, t));
    return add32((a << s) | (a >>> (32 - s)), b);
  }

  function ff(a, b, c, d, x, s, t) {
    return cmn((b & c) | (~b & d), a, b, x, s, t);
  }

  function gg(a, b, c, d, x, s, t) {
    return cmn((b & d) | (c & ~d), a, b, x, s, t);
  }

  function hh(a, b, c, d, x, s, t) {
    return cmn(b ^ c ^ d, a, b, x, s, t);
  }

  function ii(a, b, c, d, x, s, t) {
    return cmn(c ^ (b | ~d), a, b, x, s, t);
  }

  function add32(a, b) {
    return (a + b) & 0xffffffff;
  }
  if (md5("hello") != "5d41402abc4b2a76b9719d911017c592") {
    function add32(x, y) {
      var lsw = (x & 0xffff) + (y & 0xffff),
        msw = (x >> 16) + (y >> 16) + (lsw >> 16);
      return (msw << 16) | (lsw & 0xffff);
    }
  }
})();

// ========================================================== md5方法end =================================================

// ========================================================== sha256方法start ================================================= 

/* SHA256 logical functions */
function rotateRight(n, x) {
  return ((x >>> n) | (x << (32 - n)));
}
function choice(x, y, z) {
  return ((x & y) ^ (~x & z));
}
function majority(x, y, z) {
  return ((x & y) ^ (x & z) ^ (y & z));
}
function sha256_Sigma0(x) {
  return (rotateRight(2, x) ^ rotateRight(13, x) ^ rotateRight(22, x));
}
function sha256_Sigma1(x) {
  return (rotateRight(6, x) ^ rotateRight(11, x) ^ rotateRight(25, x));
}
function sha256_sigma0(x) {
  return (rotateRight(7, x) ^ rotateRight(18, x) ^ (x >>> 3));
}
function sha256_sigma1(x) {
  return (rotateRight(17, x) ^ rotateRight(19, x) ^ (x >>> 10));
}
function sha256_expand(W, j) {
  return (W[j & 0x0f] += sha256_sigma1(W[(j + 14) & 0x0f]) + W[(j + 9) & 0x0f] +
    sha256_sigma0(W[(j + 1) & 0x0f]));
}

/* Hash constant words K: */
var K256 = new Array(
  0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5,
  0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,
  0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3,
  0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,
  0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc,
  0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,
  0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7,
  0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,
  0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13,
  0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,
  0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3,
  0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,
  0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5,
  0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,
  0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208,
  0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2
);

/* global arrays */
var ihash, count, buffer;
var sha256_hex_digits = "0123456789abcdef";

/* Add 32-bit integers with 16-bit operations (bug in some JS-interpreters:
overflow) */
function safe_add256(x, y) {
  var lsw = (x & 0xffff) + (y & 0xffff);
  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);
  return (msw << 16) | (lsw & 0xffff);
}

/* Initialise the SHA256 computation */
function sha256_init() {
  ihash = new Array(8);
  count = new Array(2);
  buffer = new Array(64);
  count[0] = count[1] = 0;
  ihash[0] = 0x6a09e667;
  ihash[1] = 0xbb67ae85;
  ihash[2] = 0x3c6ef372;
  ihash[3] = 0xa54ff53a;
  ihash[4] = 0x510e527f;
  ihash[5] = 0x9b05688c;
  ihash[6] = 0x1f83d9ab;
  ihash[7] = 0x5be0cd19;
}

/* Transform a 512-bit message block */
function sha256_transform() {
  var a, b, c, d, e, f, g, h, T1, T2;
  var W = new Array(16);

  /* Initialize registers with the previous intermediate value */
  a = ihash[0];
  b = ihash[1];
  c = ihash[2];
  d = ihash[3];
  e = ihash[4];
  f = ihash[5];
  g = ihash[6];
  h = ihash[7];

  /* make 32-bit words */
  for (var i = 0; i < 16; i++)
    W[i] = ((buffer[(i << 2) + 3]) | (buffer[(i << 2) + 2] << 8) | (buffer[(i << 2) + 1]
      << 16) | (buffer[i << 2] << 24));

  for (var j = 0; j < 64; j++) {
    T1 = h + sha256_Sigma1(e) + choice(e, f, g) + K256[j];
    if (j < 16) T1 += W[j];
    else T1 += sha256_expand(W, j);
    T2 = sha256_Sigma0(a) + majority(a, b, c);
    h = g;
    g = f;
    f = e;
    e = safe_add256(d, T1);
    d = c;
    c = b;
    b = a;
    a = safe_add256(T1, T2);
  }

  /* Compute the current intermediate hash value */
  ihash[0] += a;
  ihash[1] += b;
  ihash[2] += c;
  ihash[3] += d;
  ihash[4] += e;
  ihash[5] += f;
  ihash[6] += g;
  ihash[7] += h;
}

/* Read the next chunk of data and update the SHA256 computation */
function sha256_update(data, inputLen) {
  var i, index, curpos = 0;
  /* Compute number of bytes mod 64 */
  index = ((count[0] >> 3) & 0x3f);
  var remainder = (inputLen & 0x3f);

  /* Update number of bits */
  if ((count[0] += (inputLen << 3)) < (inputLen << 3)) count[1]++;
  count[1] += (inputLen >> 29);

  /* Transform as many times as possible */
  for (i = 0; i + 63 < inputLen; i += 64) {
    for (var j = index; j < 64; j++)
      buffer[j] = data.charCodeAt(curpos++);
    sha256_transform();
    index = 0;
  }

  /* Buffer remaining input */
  for (var j = 0; j < remainder; j++)
    buffer[j] = data.charCodeAt(curpos++);
}

/* Finish the computation by operations such as padding */
function sha256_final() {
  var index = ((count[0] >> 3) & 0x3f);
  buffer[index++] = 0x80;
  if (index <= 56) {
    for (var i = index; i < 56; i++)
      buffer[i] = 0;
  } else {
    for (var i = index; i < 64; i++)
      buffer[i] = 0;
    sha256_transform();
    for (var i = 0; i < 56; i++)
      buffer[i] = 0;
  }
  buffer[56] = (count[1] >>> 24) & 0xff;
  buffer[57] = (count[1] >>> 16) & 0xff;
  buffer[58] = (count[1] >>> 8) & 0xff;
  buffer[59] = count[1] & 0xff;
  buffer[60] = (count[0] >>> 24) & 0xff;
  buffer[61] = (count[0] >>> 16) & 0xff;
  buffer[62] = (count[0] >>> 8) & 0xff;
  buffer[63] = count[0] & 0xff;
  sha256_transform();
}

/* Split the internal hash values into an array of bytes */
function sha256_encode_bytes() {
  var j = 0;
  var output = new Array(32);
  for (var i = 0; i < 8; i++) {
    output[j++] = ((ihash[i] >>> 24) & 0xff);
    output[j++] = ((ihash[i] >>> 16) & 0xff);
    output[j++] = ((ihash[i] >>> 8) & 0xff);
    output[j++] = (ihash[i] & 0xff);
  }
  return output;
}

/* Get the internal hash as a hex string */
function sha256_encode_hex() {
  var output = new String();
  for (var i = 0; i < 8; i++) {
    for (var j = 28; j >= 0; j -= 4)
      output += sha256_hex_digits.charAt((ihash[i] >>> j) & 0x0f);
  }
  return output;
}

/* Main function: returns a hex string representing the SHA256 value of the
given data */
function sha256_digest(data) {
  sha256_init();
  sha256_update(data, data.length);
  sha256_final();
  return sha256_encode_hex();
}

/* test if the JS-interpreter is working properly */

// ========================================================== sha256方法end ================================================= 

// ========================================================== 导出函数start =================================================

Global.P8QuickGameSDK = P8QuickGameSDK;

// ========================================================== 导出函数end =================================================
