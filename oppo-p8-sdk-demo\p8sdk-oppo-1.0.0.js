console.log('[ p8oppo.js ] >')
var P8OppoSDK = {};
var GameLogin = null;
var ReSignInHuawei = null;
var P8_QuickGame_Data = {
	model: "",
	engineVersion: "",
	token: "",
	aid: "",
	appid: "",
	appName: "",
	channelid: "241",
	key: "",
	site: "",
	pkgName: "",
	sid: "",
	sname: "",
	modeltype: "android",
	platform: "android",
	gameversion: "",
	device_model: "",
	device_resolution: "",
	device_version: "",
	vip: "",
	gold: "",
	level: "",
	rolename: "1",
	device: "",
	device_type: "1",
	data_url: "",
	uid: "",
	merchantId: "",
	extraInfo: "",
	merchantName: "",
	publicKey: "",
	backcallUrl: "",
	sign: ""
};

var win = (function () {
	// 尝试使用typeof来检查window对象是否存在，而不是通过try-catch
	if (typeof window !== "undefined") {
		return window;
	}
	// 如果window不存在，则返回GameGlobal（假设这是非浏览器环境中的全局对象）
	// 注意：如果GameGlobal也可能不存在，你应该添加一个额外的检查或默认值
	return GameGlobal || {}; // 提供一个空对象作为默认值，以防GameGlobal也不存在
})();


function keyValueConnect(e) {
	let a = "";
	for (const t in e) {
		if (e.hasOwnProperty.call(e, t)) {
			const n = e[t];
			a += t + "=" + n + "&"
		}
	}
	a = a.substring(0, a.length - 1);
	return a
}

function getMerchantData() {
	let e = new Promise((c, e) => {
		P8Log("获取服务器数据1");
		let t = `${P8_QuickGame_Data.platform_url}/fast_game_callback/getchannelother`;
		let a = {
			site: P8_QuickGame_Data.site,
			channel: P8_QuickGame_Data.channelid,
			version: "1.0",
			function: "getconfig"
		};
		XmlHttpRequest(t + "?data=" + JSON.stringify(a), "GET", null, a => {
			if (a.result == 0) {
				P8Log("获取服务参数成功" + getTime() + "传入参数:" + t + "?data=");
				let e = a.data[0];
				P8_QuickGame_Data.appKey = e.appkey;
				P8_QuickGame_Data.appSecret = e.appsecret;
				P8_QuickGame_Data.backcallUrl = e.backcallUrl;
				P8_QuickGame_Data.extraInfo = e.extraInfo;
				P8Log("术良data" + JSON.stringify(a));
				P8Log("术良res" + JSON.stringify(e));
				P8Log("appKey" + e.appkey);
				P8Log("appSecret" + e.appsecret);
				qg.login({
					success: function (e) {
						P8_QuickGame_Data.token = e.token;
						console.log("qg.login 返回的res", JSON.stringify(e));
						var a = e.data;
						let t = (new Date)
							.getTime();
						console.log(" P8_QuickGame_Data.appKey :", P8_QuickGame_Data.appKey);
						console.log(" P8_QuickGame_Data.appSecret :", P8_QuickGame_Data.appSecret);
						let n = "appKey=" + P8_QuickGame_Data.appKey + "&" + "appSecret=" + P8_QuickGame_Data.appSecret + "&" + "pkgName=" + P8_QuickGame_Data.pkgName + "&" + "timeStamp=" + t + "&" + "token=" + a.token;
						let i = hex_md5(n);
						i = i.toLocaleUpperCase();
						console.log("md5 要转换成大写:", i);
						let r = "https://play.open.oppomobile.com/instant-game-open/userInfo";
						let o = "pkgName=" + P8_QuickGame_Data.pkgName + "&timeStamp=" + t + "&token=" + a.token + "&sign=" + i + "&version=1.0.0";
						console.log("请求登陆的参数：" + JSON.stringify(o));
						XmlHttpRequest(r + "?" + o, "GET", null, e => {
							console.log("返回的玩家信息:" + JSON.stringify(e));
							c(e)
						})
					},
					fail: function (e) {
						c(e);
						console.log("登陆失败数据", JSON.stringify(e))
					}
				})
			}
		})
	});
	return e
}

function XmlHttpRequest(e, a = "post", t = {}, n = null, i = true) {
	let r = new XMLHttpRequest;
	r.open(a, e);
	if (i) {
		r.setRequestHeader("Content-type", "application/json")
	} else {
		console.log("术良 url" + e);
		r.setRequestHeader("Content-type", "application/x-www-form-urlencoded")
	}
	r.send(JSON.stringify(t));
	r.onreadystatechange = function () {
		if (r.status == 200 && r.readyState == 4) {
			let e = JSON.parse(r.responseText);
			console.log("请求返回的数据是什么:" + JSON.stringify(e));
			if (n) {
				n(e)
			}
		}
	}
}

function P8Log(e) {
	console.log(" ======== P8SDK ========>" + e)
}

function getItem(e) {
	return localStorage.getItem(e)
}

function setItem(e, a) {
	localStorage.setItem(e, a)
}

function getTime() {
	var e = new Date;
	let a = e.getFullYear();
	let t = e.getMonth() + 1;
	let n = e.getDate();
	let i = e.getHours();
	let r = e.getMinutes();
	let o = e.getSeconds();
	return a + "/" + t + "/" + n + " " + i + ":" + r + ":" + o
}
P8OppoSDK.Login = function e(a) {
	console.log('[ a ] >', a)
	var t = ["aid", "appid", "key", "site", "pkgName"];
	for (const r of t) {
		if (!a[r] || a[r] == "") {
			var n = {
				result: 1,
				data: {
					msg: `${r}不能为空`
				}
			};
			return new Promise((e, a) => {
				e(n)
			})
		}
	}
	P8_QuickGame_Data.site = a.site;
	P8_QuickGame_Data.key = a.key;
	P8_QuickGame_Data.aid = a.aid;
	P8_QuickGame_Data.channelid = P8_QuickGame_Data.channelid;
	P8_QuickGame_Data.appid = a.appid;
	P8_QuickGame_Data.appName = a.appName;
	P8_QuickGame_Data.pkgName = a.pkgName;
	P8Log("site" + a.site);
	P8Log("key" + a.key);
	P8Log("aid" + a.aid);
	P8Log("channel" + P8_QuickGame_Data.channelid);
	P8Log("appid" + a.appid);
	P8Log("appName" + a.appName);
	P8Log("P8SDK初始化 " + getTime());
	qg.getSystemInfo({
		success: function (e) {
			console.log("获取设备信息成功:" + JSON.stringify(e));
			P8_QuickGame_Data.engineVersion = e.platformVersionCode;
			P8_QuickGame_Data.model = e.model
		},
		fail: function (e) {
			console.log("获取设备信息失败:" + JSON.stringify(e))
		}
	});
	let i = new Promise((c, e) => {
		let a = "https://center.play800.cn/fast_game_callback/getaliyuntxt";
		console.log("请求术良");
		XmlHttpRequest(a + "?url=https://ks3-cn-shanghai.ksyun.com/aliyun/93615202390552966.txt", "GET", null, a => {
			console.log("请求术良数据" + JSON.stringify(a));
			if (a.url_address) {
				var t = a.url_address.data_url;
				var n = a.url_address.rg_url;
				var i = a.url_address.platform_url;
				let e = a.extraInfo;
				P8Log("开始获取服务器数据 " + getTime());
				P8Log("data_url=" + t + ", rg_url=" + n + ", platform_url=" + i);
				Object.assign(P8_QuickGame_Data, {
					data_url: t,
					rg_url: n,
					platform_url: i,
					extraInfo: e
				});
				getMerchantData()
					.then(e => {
						let a = e.userInfo ? e.userInfo : null;
						if (!a) {
							c({
								result: 1,
								data: {
									msg: "获取oppo userInfo 数据失败"
								}
							})
						}
						let t = a.userId;
						P8_QuickGame_Data.playerId = t;
						let n = `${P8_QuickGame_Data.platform_url}/api/createChannelUser`;
						let i = parseInt((new Date)
							.getTime() / 1e3);
						var r = hex_md5(`${P8_QuickGame_Data.key}WX${P8_QuickGame_Data.site}WX${i}${i}`);
						let o = {
							channelid: P8_QuickGame_Data.channelid,
							aid: P8_QuickGame_Data.aid,
							site: P8_QuickGame_Data.site,
							channelUid: t,
							adid: t,
							udid: t,
							channelUserName: "",
							sign: r,
							time: i
						};
						P8Log("XmlHttpRequest  2021+ url=" + n + ", data==" + JSON.stringify(o));
						XmlHttpRequest(n, "POST", o, e => {
							P8Log("play800登录返回 res=" + JSON.stringify(e));
							if (e.result == 0) {
								P8Log("获取uid成功~" + getTime());
								P8_QuickGame_Data.uid = e.data.uid;
								e.userInfo = a;
								console.log(" play800  log 2021 -06/30 login : ", JSON.stringify(e));
								c(e)
							} else {
								P8Log("获取aid失败~");
								c(e)
							}
						})
					})
			} else {
				let e = {
					result: 1,
					data: {
						msg: "获取数据失败"
					}
				};
				c(e);
				console.log("play800 init 获取服务器数据失败")
			}
		})
	});
	return i
};
P8OppoSDK.pay = function e(o) {
	let c = (new Date)
		.getTime();
	let t = {
		appId: P8_QuickGame_Data.appid,
		openId: P8_QuickGame_Data.token,
		timestamp: c,
		productName: o.productName,
		productDesc: o.productDesc,
		count: o.count,
		price: o.price,
		currency: "CNY",
		callBackUrl: P8_QuickGame_Data.backcallUrl,
		cpOrderId: o.cpOrderId,
		appVersion: o.appVersion,
		engineVersion: P8_QuickGame_Data.engineVersion,
		model: P8_QuickGame_Data.model,
		attach: o.attach ? o.attach : "附加信息"
	};
	P8Log("执行pay 2021");
	let a = new Promise(function (i, e) {
		let a = {
			site: P8_QuickGame_Data.site,
			channel: P8_QuickGame_Data.channelid,
			version: "1.0",
			function: "getsign"
		};
		Object.assign(a, t);
		let r = `${P8_QuickGame_Data.platform_url}/fast_game_callback/getchannelother`;
		console.log("sign 请求参数:" + JSON.stringify(a));
		XmlHttpRequest(r + "?data=" + JSON.stringify(a), "GET", null, a => {
			console.log("请求术良sign返回的是" + JSON.stringify(a));
			if (a.result == 0) {
				console.log("获取服务sign成功" + a.data.sign + getTime());
				t.sign = a.data.sign;
				console.log("支付请求参数 : ", JSON.stringify(t));
				let e = "https://jits.open.oppomobile.com/jitsopen/api/pay/v1.0/preOrder";
				XmlHttpRequest(e, "POST", t, a => {
					console.log("oppo 支付返回的数据" + JSON.stringify(a));
					if (a.code == 200) {
						let t = a.data;
						let e = {
							site: P8_QuickGame_Data.site,
							channel: P8_QuickGame_Data.channelid,
							version: "1.0",
							function: "getsign",
							appKey: P8_QuickGame_Data.appKey,
							orderNo: t.orderNo,
							timestamp: c
						};
						console.log("请求二次sign 请求参数:" + JSON.stringify(e));
						XmlHttpRequest(r + "?data=" + JSON.stringify(e), "GET", null, a => {
							console.log("请求术良二次sign返回的是" + JSON.stringify(a));
							if (a.result == 0) {
								console.log("获取 二次sign成功" + a.data.sign + getTime());
								let e = a.data.sign;
								qg.pay({
									appId: P8_QuickGame_Data.appid,
									token: P8_QuickGame_Data.token,
									timestamp: c,
									paySign: e,
									orderNo: t.orderNo,
									success: function (e) {
										console.log("支付成功", JSON.stringify(e.data));
										i(e)
									},
									fail: function (e) {
										console.log("支付失败", JSON.stringify(e));
										i(e)
									}
								})
							} else {
								console.log("二次获取失败")
							}
						})
					} else {
						P8Log("下单没有拉起")
					}
					var e = hex_md5(`${P8_QuickGame_Data.site}${c}${P8_QuickGame_Data.key}`);
					let t = {
						serverId: o.serverId,
						username: o.userName,
						grade: o.grade,
						amount: o.price * o.count,
						desc: o.productDesc,
						productDesc: o.productDesc,
						productName: o.productName,
						orderId: o.cpOrderId,
						roleName: o.userName,
						aid: P8_QuickGame_Data.aid,
						uid: P8_QuickGame_Data.uid,
						site: P8_QuickGame_Data.site,
						channel: P8_QuickGame_Data.channelid,
						device: P8_QuickGame_Data.playerId,
						roleId: o.roleId,
						subject: P8_QuickGame_Data.appName,
						extraInfo: P8_QuickGame_Data.extraInfo,
						time: c,
						sign: e
					};
					P8Log(" 2021 支付8001 playerId : " + P8_QuickGame_Data.playerId);
					P8Log(" 2021 支付8001 appName : " + P8_QuickGame_Data.appName);
					P8Log(" 2021 支付8001 extraInfo : " + JSON.stringify(P8_QuickGame_Data.extraInfo));
					P8Log(" 2021 支付8001 sdkParams : " + JSON.stringify(t));
					let n = `${P8_QuickGame_Data.platform_url}/sdk/createorder`;
					XmlHttpRequest(n, "POST", t, e => {
						console.log("p8这边调用下单参数:" + JSON.stringify(e))
					})
				})
			}
		}, false)
	});
	return a
};
var V = V || {};
V.Security = V.Security || {};
(function () {
	var G = V.Security;
	G.maxExactInt = Math.pow(2, 53);
	G.toUtf8ByteArr = function (e) {
		var a = [],
			t;
		for (var n = 0; n < e.length; n++) {
			t = e.charCodeAt(n);
			if (55296 <= t && t <= 56319) {
				var i = t,
					r = e.charCodeAt(n + 1);
				t = (i - 55296) * 1024 + (r - 56320) + 65536;
				n++
			}
			if (t <= 127) {
				a[a.length] = t
			} else if (t <= 2047) {
				a[a.length] = (t >>> 6) + 192;
				a[a.length] = t & 63 | 128
			} else if (t <= 65535) {
				a[a.length] = (t >>> 12) + 224;
				a[a.length] = t >>> 6 & 63 | 128;
				a[a.length] = t & 63 | 128
			} else if (t <= 1114111) {
				a[a.length] = (t >>> 18) + 240;
				a[a.length] = t >>> 12 & 63 | 128;
				a[a.length] = t >>> 6 & 63 | 128;
				a[a.length] = t & 63 | 128
			} else {
				throw "Unicode standart supports code points up-to U+10FFFF"
			}
		}
		return a
	};
	G.toHex32 = function (e) {
		if (e & 2147483648) {
			e = e & ~2147483648;
			e += Math.pow(2, 31)
		}
		var a = e.toString(16);
		while (a.length < 8) {
			a = "0" + a
		}
		return a
	};
	G.reverseBytes = function (e) {
		var a = 0;
		a += e >>> 24 & 255;
		a += (e >>> 16 & 255) << 8;
		a += (e >>> 8 & 255) << 16;
		a += (e & 255) << 24;
		return a
	};
	G.leftRotate = function (e, a) {
		return e << a | e >>> 32 - a
	};
	G.md5 = function (e) {
		var a = [7, 12, 17, 22, 7, 12, 17, 22, 7, 12, 17, 22, 7, 12, 17, 22, 5, 9, 14, 20, 5, 9, 14, 20, 5, 9, 14, 20, 5, 9, 14, 20, 4, 11, 16, 23, 4, 11, 16, 23, 4, 11, 16, 23, 4, 11, 16, 23, 6, 10, 15, 21, 6, 10, 15, 21, 6, 10, 15, 21, 6, 10, 15, 21];
		var t = [];
		for (var n = 0; n <= 63; n++) {
			t[n] = Math.abs(Math.sin(n + 1)) * Math.pow(2, 32) << 0
		}
		var i = 1732584193,
			r = 4023233417,
			o = 2562383102,
			c = 271733878,
			l, d;
		l = G.toUtf8ByteArr(e);
		e = null;
		d = l.length;
		l.push(128);
		var u = Math.abs(448 - l.length * 8 % 512) / 8;
		while (u--) {
			l.push(0)
		}
		l.push(d * 8 & 255, d * 8 >> 8 & 255, d * 8 >> 16 & 255, d * 8 >> 24 & 255);
		var n = 4;
		while (n--) {
			l.push(0)
		}
		var s = G.leftRotate;
		var n = 0,
			m = [];
		while (n < l.length) {
			for (var g = 0; g <= 15; g++) {
				m[g] = (l[n + 4 * g] << 0) + (l[n + 4 * g + 1] << 8) + (l[n + 4 * g + 2] << 16) + (l[n + 4 * g + 3] << 24)
			}
			var f = i,
				_ = r,
				p = o,
				h = c,
				k, P;
			for (var g = 0; g <= 63; g++) {
				if (g <= 15) {
					k = _ & p | ~_ & h;
					P = g
				} else if (g <= 31) {
					k = h & _ | ~h & p;
					P = (5 * g + 1) % 16
				} else if (g <= 47) {
					k = _ ^ p ^ h;
					P = (3 * g + 5) % 16
				} else {
					k = p ^ (_ | ~h);
					P = 7 * g % 16
				}
				var y = h;
				h = p;
				p = _;
				_ = _ + s(f + k + t[g] + m[P], a[g]);
				f = y
			}
			i = i + f << 0;
			r = r + _ << 0;
			o = o + p << 0;
			c = c + h << 0;
			n += 512 / 8
		}
		var v = D(i) + D(r) + D(o) + D(c);

		function D(e) {
			return G.toHex32(G.reverseBytes(e))
		}
		return v
	}
})();
var hexcase = 0;
var b64pad = "";

function hex_md5(e) {
	return rstr2hex(rstr_md5(str2rstr_utf8(e)))
}

function rstr_md5(e) {
	return binl2rstr(binl_md5(rstr2binl(e), e.length * 8))
}

function rstr2hex(e) {
	try {
		hexcase
	} catch (e) {
		hexcase = 0
	}
	var a = hexcase ? "0123456789ABCDEF" : "0123456789abcdef";
	var t = "";
	var n;
	for (var i = 0; i < e.length; i++) {
		n = e.charCodeAt(i);
		t += a.charAt(n >>> 4 & 15) + a.charAt(n & 15)
	}
	return t
}

function str2rstr_utf8(e) {
	return unescape(encodeURI(e))
}

function rstr2binl(e) {
	var a = Array(e.length >> 2);
	for (var t = 0; t < a.length; t++) a[t] = 0;
	for (var t = 0; t < e.length * 8; t += 8) a[t >> 5] |= (e.charCodeAt(t / 8) & 255) << t % 32;
	return a
}

function binl2rstr(e) {
	var a = "";
	for (var t = 0; t < e.length * 32; t += 8) a += String.fromCharCode(e[t >> 5] >>> t % 32 & 255);
	return a
}

function binl_md5(e, a) {
	e[a >> 5] |= 128 << a % 32;
	e[(a + 64 >>> 9 << 4) + 14] = a;
	var t = 1732584193;
	var n = -271733879;
	var i = -1732584194;
	var r = 271733878;
	for (var o = 0; o < e.length; o += 16) {
		var c = t;
		var l = n;
		var d = i;
		var u = r;
		t = md5_ff(t, n, i, r, e[o + 0], 7, -680876936);
		r = md5_ff(r, t, n, i, e[o + 1], 12, -389564586);
		i = md5_ff(i, r, t, n, e[o + 2], 17, 606105819);
		n = md5_ff(n, i, r, t, e[o + 3], 22, -1044525330);
		t = md5_ff(t, n, i, r, e[o + 4], 7, -176418897);
		r = md5_ff(r, t, n, i, e[o + 5], 12, 1200080426);
		i = md5_ff(i, r, t, n, e[o + 6], 17, -1473231341);
		n = md5_ff(n, i, r, t, e[o + 7], 22, -45705983);
		t = md5_ff(t, n, i, r, e[o + 8], 7, 1770035416);
		r = md5_ff(r, t, n, i, e[o + 9], 12, -1958414417);
		i = md5_ff(i, r, t, n, e[o + 10], 17, -42063);
		n = md5_ff(n, i, r, t, e[o + 11], 22, -1990404162);
		t = md5_ff(t, n, i, r, e[o + 12], 7, 1804603682);
		r = md5_ff(r, t, n, i, e[o + 13], 12, -40341101);
		i = md5_ff(i, r, t, n, e[o + 14], 17, -1502002290);
		n = md5_ff(n, i, r, t, e[o + 15], 22, 1236535329);
		t = md5_gg(t, n, i, r, e[o + 1], 5, -165796510);
		r = md5_gg(r, t, n, i, e[o + 6], 9, -1069501632);
		i = md5_gg(i, r, t, n, e[o + 11], 14, 643717713);
		n = md5_gg(n, i, r, t, e[o + 0], 20, -373897302);
		t = md5_gg(t, n, i, r, e[o + 5], 5, -701558691);
		r = md5_gg(r, t, n, i, e[o + 10], 9, 38016083);
		i = md5_gg(i, r, t, n, e[o + 15], 14, -660478335);
		n = md5_gg(n, i, r, t, e[o + 4], 20, -405537848);
		t = md5_gg(t, n, i, r, e[o + 9], 5, 568446438);
		r = md5_gg(r, t, n, i, e[o + 14], 9, -1019803690);
		i = md5_gg(i, r, t, n, e[o + 3], 14, -187363961);
		n = md5_gg(n, i, r, t, e[o + 8], 20, 1163531501);
		t = md5_gg(t, n, i, r, e[o + 13], 5, -1444681467);
		r = md5_gg(r, t, n, i, e[o + 2], 9, -51403784);
		i = md5_gg(i, r, t, n, e[o + 7], 14, 1735328473);
		n = md5_gg(n, i, r, t, e[o + 12], 20, -1926607734);
		t = md5_hh(t, n, i, r, e[o + 5], 4, -378558);
		r = md5_hh(r, t, n, i, e[o + 8], 11, -2022574463);
		i = md5_hh(i, r, t, n, e[o + 11], 16, 1839030562);
		n = md5_hh(n, i, r, t, e[o + 14], 23, -35309556);
		t = md5_hh(t, n, i, r, e[o + 1], 4, -1530992060);
		r = md5_hh(r, t, n, i, e[o + 4], 11, 1272893353);
		i = md5_hh(i, r, t, n, e[o + 7], 16, -155497632);
		n = md5_hh(n, i, r, t, e[o + 10], 23, -1094730640);
		t = md5_hh(t, n, i, r, e[o + 13], 4, 681279174);
		r = md5_hh(r, t, n, i, e[o + 0], 11, -358537222);
		i = md5_hh(i, r, t, n, e[o + 3], 16, -722521979);
		n = md5_hh(n, i, r, t, e[o + 6], 23, 76029189);
		t = md5_hh(t, n, i, r, e[o + 9], 4, -640364487);
		r = md5_hh(r, t, n, i, e[o + 12], 11, -421815835);
		i = md5_hh(i, r, t, n, e[o + 15], 16, 530742520);
		n = md5_hh(n, i, r, t, e[o + 2], 23, -995338651);
		t = md5_ii(t, n, i, r, e[o + 0], 6, -198630844);
		r = md5_ii(r, t, n, i, e[o + 7], 10, 1126891415);
		i = md5_ii(i, r, t, n, e[o + 14], 15, -1416354905);
		n = md5_ii(n, i, r, t, e[o + 5], 21, -57434055);
		t = md5_ii(t, n, i, r, e[o + 12], 6, 1700485571);
		r = md5_ii(r, t, n, i, e[o + 3], 10, -1894986606);
		i = md5_ii(i, r, t, n, e[o + 10], 15, -1051523);
		n = md5_ii(n, i, r, t, e[o + 1], 21, -2054922799);
		t = md5_ii(t, n, i, r, e[o + 8], 6, 1873313359);
		r = md5_ii(r, t, n, i, e[o + 15], 10, -30611744);
		i = md5_ii(i, r, t, n, e[o + 6], 15, -1560198380);
		n = md5_ii(n, i, r, t, e[o + 13], 21, 1309151649);
		t = md5_ii(t, n, i, r, e[o + 4], 6, -145523070);
		r = md5_ii(r, t, n, i, e[o + 11], 10, -1120210379);
		i = md5_ii(i, r, t, n, e[o + 2], 15, 718787259);
		n = md5_ii(n, i, r, t, e[o + 9], 21, -343485551);
		t = safe_add(t, c);
		n = safe_add(n, l);
		i = safe_add(i, d);
		r = safe_add(r, u)
	}
	return Array(t, n, i, r)
}

function md5_cmn(e, a, t, n, i, r) {
	return safe_add(bit_rol(safe_add(safe_add(a, e), safe_add(n, r)), i), t)
}

function md5_ff(e, a, t, n, i, r, o) {
	return md5_cmn(a & t | ~a & n, e, a, i, r, o)
}

function md5_gg(e, a, t, n, i, r, o) {
	return md5_cmn(a & n | t & ~n, e, a, i, r, o)
}

function md5_hh(e, a, t, n, i, r, o) {
	return md5_cmn(a ^ t ^ n, e, a, i, r, o)
}

function md5_ii(e, a, t, n, i, r, o) {
	return md5_cmn(t ^ (a | ~n), e, a, i, r, o)
}

function safe_add(e, a) {
	var t = (e & 65535) + (a & 65535);
	var n = (e >> 16) + (a >> 16) + (t >> 16);
	return n << 16 | t & 65535
}

function bit_rol(e, a) {
	return e << a | e >>> 32 - a
}

function md5cycle(e, a) {
	var t = e[0],
		n = e[1],
		i = e[2],
		r = e[3];
	t = ff(t, n, i, r, a[0], 7, -680876936);
	r = ff(r, t, n, i, a[1], 12, -389564586);
	i = ff(i, r, t, n, a[2], 17, 606105819);
	n = ff(n, i, r, t, a[3], 22, -1044525330);
	t = ff(t, n, i, r, a[4], 7, -176418897);
	r = ff(r, t, n, i, a[5], 12, 1200080426);
	i = ff(i, r, t, n, a[6], 17, -1473231341);
	n = ff(n, i, r, t, a[7], 22, -45705983);
	t = ff(t, n, i, r, a[8], 7, 1770035416);
	r = ff(r, t, n, i, a[9], 12, -1958414417);
	i = ff(i, r, t, n, a[10], 17, -42063);
	n = ff(n, i, r, t, a[11], 22, -1990404162);
	t = ff(t, n, i, r, a[12], 7, 1804603682);
	r = ff(r, t, n, i, a[13], 12, -40341101);
	i = ff(i, r, t, n, a[14], 17, -1502002290);
	n = ff(n, i, r, t, a[15], 22, 1236535329);
	t = gg(t, n, i, r, a[1], 5, -165796510);
	r = gg(r, t, n, i, a[6], 9, -1069501632);
	i = gg(i, r, t, n, a[11], 14, 643717713);
	n = gg(n, i, r, t, a[0], 20, -373897302);
	t = gg(t, n, i, r, a[5], 5, -701558691);
	r = gg(r, t, n, i, a[10], 9, 38016083);
	i = gg(i, r, t, n, a[15], 14, -660478335);
	n = gg(n, i, r, t, a[4], 20, -405537848);
	t = gg(t, n, i, r, a[9], 5, 568446438);
	r = gg(r, t, n, i, a[14], 9, -1019803690);
	i = gg(i, r, t, n, a[3], 14, -187363961);
	n = gg(n, i, r, t, a[8], 20, 1163531501);
	t = gg(t, n, i, r, a[13], 5, -1444681467);
	r = gg(r, t, n, i, a[2], 9, -51403784);
	i = gg(i, r, t, n, a[7], 14, 1735328473);
	n = gg(n, i, r, t, a[12], 20, -1926607734);
	t = hh(t, n, i, r, a[5], 4, -378558);
	r = hh(r, t, n, i, a[8], 11, -2022574463);
	i = hh(i, r, t, n, a[11], 16, 1839030562);
	n = hh(n, i, r, t, a[14], 23, -35309556);
	t = hh(t, n, i, r, a[1], 4, -1530992060);
	r = hh(r, t, n, i, a[4], 11, 1272893353);
	i = hh(i, r, t, n, a[7], 16, -155497632);
	n = hh(n, i, r, t, a[10], 23, -1094730640);
	t = hh(t, n, i, r, a[13], 4, 681279174);
	r = hh(r, t, n, i, a[0], 11, -358537222);
	i = hh(i, r, t, n, a[3], 16, -722521979);
	n = hh(n, i, r, t, a[6], 23, 76029189);
	t = hh(t, n, i, r, a[9], 4, -640364487);
	r = hh(r, t, n, i, a[12], 11, -421815835);
	i = hh(i, r, t, n, a[15], 16, 530742520);
	n = hh(n, i, r, t, a[2], 23, -995338651);
	t = ii(t, n, i, r, a[0], 6, -198630844);
	r = ii(r, t, n, i, a[7], 10, 1126891415);
	i = ii(i, r, t, n, a[14], 15, -1416354905);
	n = ii(n, i, r, t, a[5], 21, -57434055);
	t = ii(t, n, i, r, a[12], 6, 1700485571);
	r = ii(r, t, n, i, a[3], 10, -1894986606);
	i = ii(i, r, t, n, a[10], 15, -1051523);
	n = ii(n, i, r, t, a[1], 21, -2054922799);
	t = ii(t, n, i, r, a[8], 6, 1873313359);
	r = ii(r, t, n, i, a[15], 10, -30611744);
	i = ii(i, r, t, n, a[6], 15, -1560198380);
	n = ii(n, i, r, t, a[13], 21, 1309151649);
	t = ii(t, n, i, r, a[4], 6, -145523070);
	r = ii(r, t, n, i, a[11], 10, -1120210379);
	i = ii(i, r, t, n, a[2], 15, 718787259);
	n = ii(n, i, r, t, a[9], 21, -343485551);
	e[0] = add32(t, e[0]);
	e[1] = add32(n, e[1]);
	e[2] = add32(i, e[2]);
	e[3] = add32(r, e[3])
}

function cmn(e, a, t, n, i, r) {
	a = add32(add32(a, e), add32(n, r));
	return add32(a << i | a >>> 32 - i, t)
}

function ff(e, a, t, n, i, r, o) {
	return cmn(a & t | ~a & n, e, a, i, r, o)
}

function gg(e, a, t, n, i, r, o) {
	return cmn(a & n | t & ~n, e, a, i, r, o)
}

function hh(e, a, t, n, i, r, o) {
	return cmn(a ^ t ^ n, e, a, i, r, o)
}

function ii(e, a, t, n, i, r, o) {
	return cmn(t ^ (a | ~n), e, a, i, r, o)
}

function md51(e) {
	var a = "";
	var t = e.length,
		n = [1732584193, -271733879, -1732584194, 271733878],
		i;
	for (i = 64; i <= e.length; i += 64) {
		md5cycle(n, md5blk(e.substring(i - 64, i)))
	}
	e = e.substring(i - 64);
	var r = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
	for (i = 0; i < e.length; i++) r[i >> 2] |= e.charCodeAt(i) << (i % 4 << 3);
	r[i >> 2] |= 128 << (i % 4 << 3);
	if (i > 55) {
		md5cycle(n, r);
		for (i = 0; i < 16; i++) r[i] = 0
	}
	r[14] = t * 8;
	md5cycle(n, r);
	return n
}

function md5blk(e) {
	var a = [],
		t;
	for (t = 0; t < 64; t += 4) {
		a[t >> 2] = e.charCodeAt(t) + (e.charCodeAt(t + 1) << 8) + (e.charCodeAt(t + 2) << 16) + (e.charCodeAt(t + 3) << 24)
	}
	return a
}
var hex_chr = "0123456789abcdef".split("");

function rhex(e) {
	var a = "",
		t = 0;
	for (; t < 4; t++) a += hex_chr[e >> t * 8 + 4 & 15] + hex_chr[e >> t * 8 & 15];
	return a
}

function hex(e) {
	for (var a = 0; a < e.length; a++) e[a] = rhex(e[a]);
	return e.join("")
}

function md5(e) {
	return hex(md51(e))
}

function add32(e, a) {
	return e + a & 4294967295
}
if (md5("hello") != "5d41402abc4b2a76b9719d911017c592") {
	function add32(e, a) {
		var t = (e & 65535) + (a & 65535),
			n = (e >> 16) + (a >> 16) + (t >> 16);
		return n << 16 | t & 65535
	}
} (function () {
	function e(e, a) {
		var t = e[0],
			n = e[1],
			i = e[2],
			r = e[3];
		t = o(t, n, i, r, a[0], 7, -680876936);
		r = o(r, t, n, i, a[1], 12, -389564586);
		i = o(i, r, t, n, a[2], 17, 606105819);
		n = o(n, i, r, t, a[3], 22, -1044525330);
		t = o(t, n, i, r, a[4], 7, -176418897);
		r = o(r, t, n, i, a[5], 12, 1200080426);
		i = o(i, r, t, n, a[6], 17, -1473231341);
		n = o(n, i, r, t, a[7], 22, -45705983);
		t = o(t, n, i, r, a[8], 7, 1770035416);
		r = o(r, t, n, i, a[9], 12, -1958414417);
		i = o(i, r, t, n, a[10], 17, -42063);
		n = o(n, i, r, t, a[11], 22, -1990404162);
		t = o(t, n, i, r, a[12], 7, 1804603682);
		r = o(r, t, n, i, a[13], 12, -40341101);
		i = o(i, r, t, n, a[14], 17, -1502002290);
		n = o(n, i, r, t, a[15], 22, 1236535329);
		t = l(t, n, i, r, a[1], 5, -165796510);
		r = l(r, t, n, i, a[6], 9, -1069501632);
		i = l(i, r, t, n, a[11], 14, 643717713);
		n = l(n, i, r, t, a[0], 20, -373897302);
		t = l(t, n, i, r, a[5], 5, -701558691);
		r = l(r, t, n, i, a[10], 9, 38016083);
		i = l(i, r, t, n, a[15], 14, -660478335);
		n = l(n, i, r, t, a[4], 20, -405537848);
		t = l(t, n, i, r, a[9], 5, 568446438);
		r = l(r, t, n, i, a[14], 9, -1019803690);
		i = l(i, r, t, n, a[3], 14, -187363961);
		n = l(n, i, r, t, a[8], 20, 1163531501);
		t = l(t, n, i, r, a[13], 5, -1444681467);
		r = l(r, t, n, i, a[2], 9, -51403784);
		i = l(i, r, t, n, a[7], 14, 1735328473);
		n = l(n, i, r, t, a[12], 20, -1926607734);
		t = d(t, n, i, r, a[5], 4, -378558);
		r = d(r, t, n, i, a[8], 11, -2022574463);
		i = d(i, r, t, n, a[11], 16, 1839030562);
		n = d(n, i, r, t, a[14], 23, -35309556);
		t = d(t, n, i, r, a[1], 4, -1530992060);
		r = d(r, t, n, i, a[4], 11, 1272893353);
		i = d(i, r, t, n, a[7], 16, -155497632);
		n = d(n, i, r, t, a[10], 23, -1094730640);
		t = d(t, n, i, r, a[13], 4, 681279174);
		r = d(r, t, n, i, a[0], 11, -358537222);
		i = d(i, r, t, n, a[3], 16, -722521979);
		n = d(n, i, r, t, a[6], 23, 76029189);
		t = d(t, n, i, r, a[9], 4, -640364487);
		r = d(r, t, n, i, a[12], 11, -421815835);
		i = d(i, r, t, n, a[15], 16, 530742520);
		n = d(n, i, r, t, a[2], 23, -995338651);
		t = u(t, n, i, r, a[0], 6, -198630844);
		r = u(r, t, n, i, a[7], 10, 1126891415);
		i = u(i, r, t, n, a[14], 15, -1416354905);
		n = u(n, i, r, t, a[5], 21, -57434055);
		t = u(t, n, i, r, a[12], 6, 1700485571);
		r = u(r, t, n, i, a[3], 10, -1894986606);
		i = u(i, r, t, n, a[10], 15, -1051523);
		n = u(n, i, r, t, a[1], 21, -2054922799);
		t = u(t, n, i, r, a[8], 6, 1873313359);
		r = u(r, t, n, i, a[15], 10, -30611744);
		i = u(i, r, t, n, a[6], 15, -1560198380);
		n = u(n, i, r, t, a[13], 21, 1309151649);
		t = u(t, n, i, r, a[4], 6, -145523070);
		r = u(r, t, n, i, a[11], 10, -1120210379);
		i = u(i, r, t, n, a[2], 15, 718787259);
		n = u(n, i, r, t, a[9], 21, -343485551);
		e[0] = s(t, e[0]);
		e[1] = s(n, e[1]);
		e[2] = s(i, e[2]);
		e[3] = s(r, e[3])
	}

	function c(e, a, t, n, i, r) {
		a = s(s(a, e), s(n, r));
		return s(a << i | a >>> 32 - i, t)
	}

	function o(e, a, t, n, i, r, o) {
		return c(a & t | ~a & n, e, a, i, r, o)
	}

	function l(e, a, t, n, i, r, o) {
		return c(a & n | t & ~n, e, a, i, r, o)
	}

	function d(e, a, t, n, i, r, o) {
		return c(a ^ t ^ n, e, a, i, r, o)
	}

	function u(e, a, t, n, i, r, o) {
		return c(t ^ (a | ~n), e, a, i, r, o)
	}

	function s(e, a) {
		return e + a & 4294967295
	}
	if (md5("hello") != "5d41402abc4b2a76b9719d911017c592") {
		function s(e, a) {
			var t = (e & 65535) + (a & 65535),
				n = (e >> 16) + (a >> 16) + (t >> 16);
			return n << 16 | t & 65535
		}
	}
})();

win.P8OppoSDK = P8OppoSDK;