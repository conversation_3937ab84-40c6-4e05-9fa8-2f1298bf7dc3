// require('./p8sdk-oppo-1.0.2.js')
require('./p8sdk-quickGame-1.0.6.js')
// require('./p8sdk-quickGame-1.0.1-remix.js')

let data = {
    appName: '', //游戏名
    site: '', // 问运营
    key: '', // 问运营
    aid: '', // 问运营
    appid: '', // 小程序appid
    pkgName: '' // 包名
}

console.log('进来demo2');

// qg.setEnableDebug({
//     enableDebug: true, // true 为打开，false 为关闭
//     success: function () {
//         // 以下语句将会在 vConsole 面板输出
//         console.log("test consol log");
//         console.info("test console info");
//         console.warn("test consol warn");
//         console.debug("test consol debug");
//         console.error("test consol error");
//     },
//     complete: function () {},
//     fail: function () {},
// });

let btnConfigs = [{
        label: '聚合初始化', // 按钮名
        cbk: () => {
            console.log('[ 聚合初始化点击 ] >')
            let initData = {
                appName: '超级逗萌兽', //游戏名
                site: 'cjdms_android3', // 问运营
                key: 'dfeb2b178ffcb8761cb6051ed284a9b3', // 问运营
                aid: '936299170692527690', // 问运营
                appid: '33245837', // 小程序appid
                pkgName: 'com.jvwan.cjdmsxyx.nearme.gamecenter', // 包名
                platform: 'oppo', // 平台
                channelid: "241", // oppo：241 
            }
            P8QuickGameSDK.init(initData).then((res) => {
                console.log('[聚合SDK] 初始化返回数据res:', res);
            });
        }
    },
    {
        label: '聚合登录', // 按钮名
        cbk: () => {
            console.log('登录点击');
            P8QuickGameSDK.login().then((res) => {
                console.log('[聚合SDK] 登录返回数据res:', res);
            })
        }
    },
    {
        label: '聚合支付',
        cbk: () => {
            let cp_order = {
                roleId: '123', //角色ID
                userName: 'testName', // 用户名
                grade: '1', // 用户等级
                serverId: '999', //服务器id
                productName: '灵宠礼包', //商品名称
                productDesc: '灵宠礼包', //商品描述
                count: '1', //数量
                price: '600', //单位(分)
                cpOrderId: 'oppo_' + new Date().getTime(), //CP自己的订单号
                appVersion: '1.0.0', //游戏版本
                // 非必填
                extraInfo: {
                    platform: "P800_OPPOMini",
                }, // 透传的拓展参数 如需使用请传入对象object
            }
            P8QuickGameSDK.pay(cp_order).then((res) => {
                console.log('[聚合SDK] 支付返回数据res:', res);
            });
        }
    },
    {
        label: '聚合广告初始化', // 按钮名
        cbk: () => {
            console.log('[ 聚合广告初始化点击 ] >')
            P8QuickGameSDK.videoADInit({
                adUnitId: '2407640',
                adSlot: '超级逗萌兽小游戏激励视频',
                success: (res) => {
                    console.log('[聚合SDK] 激励视频 广告加载事件成功', JSON.stringify(res))
                },
                fail: (err) => {
                    console.log('[聚合SDK] 激励视频 广告加载异常', JSON.stringify(err))
                }
            });
        }
    },
    {
        label: '聚合激励视频广告', // 按钮名
        cbk: () => {
            console.log('[ 聚合激励视频广告点击 ] >')
            P8QuickGameSDK.videoADShow(() => {
                console.log('videoADShow Success');
            }, () => {
                console.log('videoADShow Close');
            }, (err) => {
                console.log('videoADShow Fail ');
            }, () => {
                console.log('videoADShow Show ');
            }, '聚合展示广告')
        }
    },
]

let btnConfigs2 = [{
        label: 'SDK初始化', // 按钮名
        cbk: () => {
            console.log('SDK初始化');
            let initData = {
                quickApp: false, //  微信小游戏为false 其余为true
                aid_android: configData.aid_android, // 必填，广告渠道ID，参数问运营
                aid_ios: configData.aid_ios, //快游戏传入aid_android的内容
                // key 和 site 至少选择一个平台
                key_android: configData.key_android, // 安卓 key   快游戏必填 参数问运营
                site_android: configData.site_android, // 安卓 site  快游戏必填 参数问运营
                key_ios: configData.key_ios, // ios  key  
                site_ios: configData.site_ios, // ios  site  游戏标识
            }
            P8LogSDK.initLogData(initData).then((data) => {
                console.log(data);
            });
        }
    },
    {
        label: 'SDK激活', // 按钮名s
        cbk: () => {
            console.log('SDK激活');
            let params = {
                // 选填 能获取到就填
                ip: '', //设备IP地址 获取不到传空字符串
                mac: '', //设备mac地址 获取不到传空字符串
                gameversion: '', //游戏版本号:1.3.1
                oaid: '', //    oaid, 安卓必须传,获取不了传空, ios不传
                ext: '', //其他信息（预留字段）
                test: ''
            }
            P8QuickGameSDK.onActiveFunc().then((res) => {
                console.log('[聚合SDK] 激活上报返回数据res:', res);
            });
        }
    },
    {
        label: '登录上报', // 按钮名s
        cbk: () => {
            console.log('登录上报');
            let loginData = {
                // 必填
                uid: '109746643',
                sid: '1999', // 游戏服务器ID
                roleid: '123', // 游戏角色ID
                rolename: 'faker', // 游戏角色名称
                level: '1', // 登录时角色等级
                vip: "0", // vip等级
                // 选填
                oaid: '', // oaid, 安卓必须传,获取不了传空, ios不传
                username: '', // Play800 sdk 用户登录账号
                onlinetime: '', // 角色累计在线时长（单位分钟）
            }
            P8QuickGameSDK.pushLoginData(loginData).then((data) => {
                console.log('[聚合SDK] 登录上报返回数据res:', data);
                // 初始化成功 
            });
        }
    },
    {
        label: '充值上报',
        cbk: () => {
            let income_order = {
                username: 'ekko', // 用户账号
                sid: '1994', // 服务器 ID
                roleid: 'ekko', // 角色 ID
                rolename: 'ekko', // 角色名
                level: '1', // 角色等级
                order_id: '999999', // 订单 ID
                income_channel: 'huawei_240', // 充值渠道 华为快游戏是：huawei_240
                income_currency: 'yuan', // 充值币种
                income_money: '33', // 充值金额,单位元
            }
            P8QuickGameSDK.incomeLog(income_order).then((data) => {
                console.log('[聚合SDK] 充值上报返回数据res:', data)
            })
        }
    },
    {
        label: '广告上报',
        cbk: () => {
            let arg = {
                sid: '999', // 游戏服id
                roleid: 'ekko', //角色id
                rolename: 'ekko', // 角色名
                level: '1', // 角色等级
                uid: '109549532',
                ad_slot: '激励0906', // 广告位创建的名称 在微信后台申请的广告位的名称
                ad_unit_id: 'adunit-0613990ef470e7e4', //广告位id
                type: 'RewardedVideoAd', // 'BannerAd' 横幅 'RewardedVideoAd' 激励视频 'InterstitialAd' 插屏广告 'CustomAd' 模板广告
                status: '0', // 点击传入 0 观看成功传入 1 banner广告点击就算成功
            }
            P8QuickGameSDK.wxVideoLog(arg).then((res) => {
                console.log('[聚合SDK] 广告上报返回数据res:', res);
            })
        }
    },
    {
        label: '新创角色上报',
        cbk: () => {
            let login_order = {
                // 必填
                uid: '109746643',
                sid: '999', // 服务器 ID
                roleid: '1', // 角色 ID
                rolename: '玩家123', // 角色名
                level: '0', // 角色等级
            }
            P8QuickGameSDK.signLog(login_order).then((res) => {
                console.log('[聚合SDK] 新创角色上报返回数据res:', res);
            })
        }
    },
    {
        label: '角色升级上报',
        cbk: () => {
            let gradeData = {
                uid: '107856888',
                level: '1', //用户等级
                sid: '999', // 服务器id
                roleid: 'faker', // 角色id
                rolename: 'faker', //角色名
                vip: '1',
                // 非必传
                onlinetime: '',
                oaid: '',
            }
            P8QuickGameSDK.upGradeRecord(gradeData).then((data) => {
                console.log('[聚合SDK] 角色升级上报返回数据res:', data);
            })
        }
    }
]

let base = cc.find('CanvasTest');
let offsetPosY = 0;
let offsetPosY2 = 0;
let windowHeight = base.height;
let windowWidth = base.width;
btnConfigs.forEach(element => {
    creatorBtn(element.label, element.cbk);
});
btnConfigs2.forEach(element => {
    creatorBtn2(element.label, element.cbk);
});


// 设置输入框节点的位置和尺寸

function createWhite() {
    const texture = new cc.Texture2D();
    const image = new Uint8Array([255, 255, 255, 255]);
    texture.initWithData(image, cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
    const spriteFrame = new cc.SpriteFrame();
    spriteFrame.setTexture(texture);
    return spriteFrame;
}

function createNode(name, parent = null, set = null) {
    let node = new cc.Node(name);
    if (parent) {
        node.parent = parent;
    } else {
        node.parent = cc.find('CanvasTest')
    }
    if (set && Object(set) === set) {
        Object.assign(node, set)
    }
    node.zIndex = cc.macro.MAX_ZINDEX; //- indexDis;
    return node;
}

function addComponent(node, type, set = null) {
    let cmp = node.addComponent(type);
    if (set && Object(set) === set) {
        Object.assign(cmp, set)
    }
    return cmp;
}
GameGlobal.createNode = createNode;
GameGlobal.addComponent = addComponent;

function hexToColor(hex) {
    const color = new cc.Color();
    color.r = parseInt(hex.substr(1, 2), 16);
    color.g = parseInt(hex.substr(3, 2), 16);
    color.b = parseInt(hex.substr(5, 2), 16);
    return color;
}

function creatorBtn(label, cbk) {
    let posx = 200,
        height = 80,
        dis = 20,
        offset = 100;
    if (!offsetPosY) {
        offsetPosY = windowHeight / 2 - height / 2 - dis
    } else {
        offsetPosY -= offset;
    }
    let svnode = createNode('scNode', null, {
        width: posx,
        height: height,
        x: -windowWidth / 2 + posx / 2 + dis,
        y: offsetPosY
    });
    let localSF = createWhite();
    // console.log('localSF:', localSF);
    addComponent(svnode, cc.Sprite, {
        sizeMode: cc.Sprite.SizeMode.CUSTOM,
        spriteFrame: localSF
    });
    svnode.on(cc.Node.EventType.TOUCH_END, cbk, this);
    let title = createNode(label, svnode, {
        x: 0,
        y: 0,
        color: cc.Color.BLACK
    });
    addComponent(title, cc.Label, {
        string: label,
        fontSize: 38,
        lineHeight: 40,
    });
}

// 后期放到sdk文件中
var CryptoJS = wx.CryptoJS; // require("./crypto-js/crypto-js");

// 辅助函数
function md5(data) {
    return CryptoJS.MD5(data).toString();
}

// 传入key之前要调用，不然结果不对
function parseKey(key) {
    return CryptoJS.enc.Utf8.parse(key);
}

// 解密过程
function decrypt(mode, cipherText, key, iv = null) {
    const uKey = parseKey(key);
    const uIv = parseKey(iv);
    let bytes = CryptoJS.AES.decrypt(cipherText, uKey, {
        iv: uIv,
        mode: mode,
        padding: CryptoJS.pad.Pkcs7
    });
    // console.log('bytes', bytes.toString(CryptoJS.enc.Base64));
    return bytes.toString(CryptoJS.enc.Utf8);
}

function test() {
    let key = "916002158107813254";
    let md5Key = md5(key);
    let data = 'Bv4uLpMxsOdEtTiQo6YTFBpVuu/b+E4lxSSESIEQjFIYKIxlqUNaeiRSj26qVlT1S10swrwIVUvu06NLdcHKS9UP2uuntv0E0loIE9/RfQAJ+8Qq6r6jjjANGP2rINX8dgvZ/nM88iiJbFEUn5o4OwPaBBzviHNZ3SRPKyoVNXxLXSzCvAhVS+7To0t1wcpL1Q/a66e2/QTSWggT39F9AKIfGU9WWBeMz740cDRBjOruuo+UljNY8yZWMUfwBF9UXt9s7V007/ba2JqINsOHxEtdLMK8CFVL7tOjS3XBykt+UfRgCHmFy61PvNIgfHetKm3v32an0gzsK1icwZEsG6GJ5hAmudMHMpvMaMGVLsAD+z1IqBi27L5Ze2IDV0TFS10swrwIVUvu06NLdcHKSzsbl41vqSWFIX08PoygdyCrHaXnFoX48CntpZxAzk3O7HzbcIfsP2dMh7ShSkYYKicbEl+QYrpMTas0CuCZ1WpLXSzCvAhVS+7To0t1wcpLAF47NQssT7Iq9RGAWnGLIU2EFoUzRGAiNXF5w1M8k+Be9rGne5mq5Nt69N2bYrPmOj6wcSi++n8epufewWigMUtdLMK8CFVL7tOjS3XByktc2XAGTBUCrZgEyQfPsMc7VsgwtLWQqU/A+UjJlM5q4NjuiILD57SI1cxoVh5HbuOL3Q4T2j6krPGRKK7vAgB3S10swrwIVUvu06NLdcHKS0ZGCSZPqsCezaw2eCQF3RuvGGxD0N3huF3fsMK2Yco69G2waBnFIDGFntx0wnFPFYvdDhPaPqSs8ZEoru8CAHdLXSzCvAhVS+7To0t1wcpLkoofGcxZmLk8Z+hlXLeQuDsZ4QPcqwfL/FAd06TLiUO/0uxkyOqqUiHu7Zmx4vQ7ciP0YtV3yA/kkqZY0PVc+UtdLMK8CFVL7tOjS3XByktTbVjwkNhW62fmPzHgLJxJWepBcrVvKkRR/m44pxFqB+hkr8E2HgvEbLu/M5xw71YD2gQc74hzWd0kTysqFTV8S10swrwIVUvu06NLdcHKS9UP2uuntv0E0loIE9/RfQDMIYmz27rt9VlhZHa9NYfFU2O8ReVTvDUCYEqlBGg0yLmpLnuzzO9WaJi5i4gFT5woxqTi/Yd9Tom/X3pbyJO7S10swrwIVUvu06NLdcHKS8jYONfmzm0X8IMj4iXPM+b4XxPT601esGoC6nG//Oe+'
    let plainText = decrypt(CryptoJS.mode.ECB, data, md5Key);
}

// test();

function creatorBtn2(label, cbk) {
    let posx = 300,
        height = 80,
        dis = 20,
        offset = 100;
    if (!offsetPosY2) {
        offsetPosY2 = windowHeight / 2 - height / 2 - dis;
    } else {
        offsetPosY2 = offsetPosY2 - offset;
    }
    let svnode = createNode('scNode', null, {
        width: posx,
        height: height,
        x: windowWidth / 2 - posx / 2 - dis, // 调整 x 的计算方式
        y: offsetPosY2
    });
    let localSF = createWhite();
    addComponent(svnode, cc.Sprite, {
        sizeMode: cc.Sprite.SizeMode.CUSTOM,
        spriteFrame: localSF
    });
    svnode.on(cc.Node.EventType.TOUCH_END, cbk, this);
    let title = createNode(label, svnode, {
        x: 0,
        y: 0,
        color: cc.Color.BLACK
    });
    addComponent(title, cc.Label, {
        string: label,
        fontSize: 38,
        lineHeight: 40,
    });
}