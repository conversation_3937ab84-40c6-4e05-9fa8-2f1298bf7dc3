!function i(o,a,r){function c(t,e){if(!a[t]){if(!o[t]){var n="function"==typeof require&&require;if(!e&&n)return n(t,!0);if(s)return s(t,!0);throw(e=new Error("Cannot find module '"+t+"'")).code="MODULE_NOT_FOUND",e}n=a[t]={exports:{}},o[t][0].call(n.exports,function(e){return c(o[t][1][e]||e)},n,n.exports,i,o,a,r)}return a[t].exports}for(var s="function"==typeof require&&require,e=0;e<r.length;e++)c(r[e]);return c}({1:[function(l,e,L){"use strict";var u,d=l("./cache-manager"),t=window.fsUtils,f=t.downloadFile,o=t.readText,a=t.readArrayBuffer,i=t.readJson,h=t.loadSubpackage,r=t.getUserDataPath,p=t._subpackagesPath,g=/^https?:\/\/.*/,t=cc.assetManager.downloader,n=cc.assetManager.parser,c=cc.assetManager.presets,m=(t.maxConcurrency=8,t.maxRequestsPerFrame=64,c.scene.maxConcurrency=10,c.scene.maxRequestsPerFrame=64,{}),_={},v={};function s(e,t,n){if("function"==typeof t&&(n=t,t=null),g.test(e))n&&n(new Error("Can not load remote scripts"));else{if(v[e])return n&&n();l(e),v[e]=!0,n&&n(null)}}function y(e,t,n){"function"==typeof t&&(n=t,t=null);t=document.createElement("audio");t.src=e,n&&n(null,t)}function b(i,t,o,e,a){var n=I(i,o);n.inLocal?t(n.url,o,a):n.inCache?(d.updateLastTime(i),t(n.url,o,function(e,t){e&&d.removeCache(i),a(e,t)})):f(i,null,o.header,e,function(e,n){e?a(e,null):t(n,o,function(e,t){e||(d.tempFiles.add(i,n),d.cacheFile(i,n,o.cacheEnabled,o.__cacheBundleRoot__,!0)),a(e,t)})})}function w(e,t,n){a(e,n)}function F(e,t,n){o(e,n)}function E(e,t,n){i(e,n)}function A(e,t,n){b(e,E,t,t.onFileProgress,n)}function S(e,t,n){var i=function(e){var t=e.lastIndexOf(".ttf");-1===t&&(t=e.lastIndexOf(".tmp"));if(-1===t)return e;var n=e.lastIndexOf("/");e=-1===n?e.substring(0,t)+"_LABEL":e.substring(n+1,t)+"_LABEL";return e}(e),e=new FontFace(i,"url('"+e+"')");document.fonts.add(e),e.load(),e.loaded.then(function(){n(null,i)},function(){cc.warnID(4933,i),n(null,i)})}function C(e,t,n){n(null,e)}function T(e,t,n){b(e,C,t,t.onFileProgress,n)}var M={};function P(e,n,i){a(e,function(e,t){if(e)return i(e);x(t,n,i)})}function j(e,n,i){a(e,function(e,t){if(e)return i(e);D(t,n,i)})}var x=n.parsePVRTex,D=n.parsePKMTex;var c=T,I=(t.downloadDomAudio=y,t.downloadScript=s,n.parsePVRTex=P,n.parsePKMTex=j,t.register({".js":s,".mp3":T,".ogg":T,".wav":T,".m4a":T,".png":c,".jpg":c,".bmp":c,".jpeg":c,".gif":c,".ico":c,".tiff":c,".image":c,".webp":c,".pvr":T,".pkm":T,".font":T,".eot":T,".ttf":T,".woff":T,".svg":T,".ttc":T,".txt":T,".xml":T,".vsh":T,".fsh":T,".atlas":T,".tmx":T,".tsx":T,".plist":T,".fnt":T,".json":A,".ExportJson":T,".binary":T,".bin":T,".dbbin":T,".skel":T,".mp4":T,".avi":T,".mov":T,".mpg":T,".mpeg":T,".rm":T,".rmvb":T,bundle:function(e,t,r){var n=cc.path.basename(e),i=t.version||cc.assetManager.downloader.bundleVers[n];if(m[n]){var o="".concat(p).concat(n,"/config.").concat(i?i+".":"","json"),a=function(){A(o,t,function(e,t){t&&(t.base="".concat(p).concat(n,"/")),r(e,t)})};if(M[n])return a();h(n,t.onFileProgress,function(e){e?r(e,null):(M[n]=!0,a())})}else{g.test(e)?(s=e,c="src/scripts/".concat(n,"/index.js"),d.makeBundleFolder(n)):_[n]?(s="".concat(u,"remote/").concat(n),c="src/scripts/".concat(n,"/index.js"),d.makeBundleFolder(n)):(s="assets/".concat(n),c="assets/".concat(n,"/index.js")),v[c]||(l(c),v[c]=!0),t.cacheEnabled=!0,t.__cacheBundleRoot__=n;var c,s,o="".concat(s,"/config.").concat(i?i+".":"","json");A(o,t,function(e,n){var i,o,a;e?r&&r(e):n.isZip?(e=n.zipVersion,e="".concat(s,"/res.").concat(e?e+".":"","zip"),i=e,o=t,a=function(e,t){e?r&&r(e):(n.base=t+"/res/",r&&r(null,n))},(e=d.cachedFiles.get(i))?(d.updateLastTime(i),a&&a(null,e.url)):g.test(i)?f(i,null,o.header,o.onFileProgress,function(e,t){e?a&&a(e):d.unzipAndCacheBundle(i,t,o.__cacheBundleRoot__,a)}):d.unzipAndCacheBundle(i,i,o.__cacheBundleRoot__,a)):(n.base=s+"/",r&&r(null,n))})}},default:function(e,t,n){b(e,F,t,t.onFileProgress,n)}}),n.register({".png":t.downloadDomImage,".jpg":t.downloadDomImage,".bmp":t.downloadDomImage,".jpeg":t.downloadDomImage,".gif":t.downloadDomImage,".ico":t.downloadDomImage,".tiff":t.downloadDomImage,".image":t.downloadDomImage,".webp":t.downloadDomImage,".pvr":P,".pkm":j,".font":S,".eot":S,".ttf":S,".woff":S,".svg":S,".ttc":S,".mp3":y,".ogg":y,".wav":y,".m4a":y,".txt":F,".xml":F,".vsh":F,".fsh":F,".atlas":F,".tmx":F,".tsx":F,".fnt":F,".plist":function(e,t,i){o(e,function(e,t){var n=null;e||(n=cc.plistParser.parse(t))||(e=new Error("parse failed")),i&&i(e,n)})},".binary":w,".bin":w,".dbbin":w,".skel":w,".ExportJson":E}),function(e,t){var n=!1,i=!1;return!e.startsWith(r())&&g.test(e)?t.reload||((t=d.cachedFiles.get(e))?(i=!0,e=t.url):(t=d.tempFiles.get(e))&&(n=!0,e=t)):n=!0,{url:e,inLocal:n,inCache:i}}),K=(cc.assetManager.transformPipeline.append(function(e){for(var t=e.output=e.input,n=0,i=t.length;n<i;n++){var o=t[n],a=o.options;o.config?a.__cacheBundleRoot__=o.config.name:a.cacheEnabled=void 0!==a.cacheEnabled&&a.cacheEnabled}}),cc.assetManager.init);cc.assetManager.init=function(e){K.call(cc.assetManager,e),e.subpackages&&e.subpackages.forEach(function(e){return m[e]="".concat(p)+e}),e.remoteBundles&&e.remoteBundles.forEach(function(e){return _[e]=!0}),(u=e.server||"")&&!u.endsWith("/")&&(u+="/"),d.init()}},{"./cache-manager":2}],2:[function(e,t,n){"use strict";var i=window.fsUtils,o=i.getUserDataPath,a=i.readJsonSync,c=i.makeDirSync,r=i.writeFileSync,l=i.copyFile,u=i.downloadFile,s=i.writeFile,d=i.deleteFile,f=i.rmdirSync,h=i.unzip,p=!1,g=null,m=!1,_=[],v=[],y=!1,b=/the maximum size of the file storage/,w=0,F=/^https?:\/\/.*/;cc.assetManager.cacheManager=t.exports={cacheDir:"gamecaches",cachedFileName:"cacheList.json",cacheEnabled:!0,autoClear:!0,cacheInterval:500,deleteInterval:500,writeFileInterval:2e3,outOfStorage:!1,tempFiles:null,cachedFiles:null,cacheQueue:{},version:"1.0",getCache:function(e){return this.cachedFiles.has(e)?this.cachedFiles.get(e).url:""},getTemp:function(e){return this.tempFiles.has(e)?this.tempFiles.get(e):""},init:function(){this.cacheDir=o()+"/"+this.cacheDir;var e=this.cacheDir+"/"+this.cachedFileName,t=a(e);t instanceof Error||!t.version?(t instanceof Error||f(this.cacheDir,!0),this.cachedFiles=new cc.AssetManager.Cache,c(this.cacheDir,!0),r(e,JSON.stringify({files:this.cachedFiles._map,version:this.version}),"utf8")):this.cachedFiles=new cc.AssetManager.Cache(t.files),this.tempFiles=new cc.AssetManager.Cache},updateLastTime:function(e){this.cachedFiles.has(e)&&(this.cachedFiles.get(e).lastTime=Date.now())},_write:function(){m=!(g=null),s(this.cacheDir+"/"+this.cachedFileName,JSON.stringify({files:this.cachedFiles._map,version:this.version}),"utf8",function(){m=!1;for(var e=0,t=v.length;e<t;e++)v[e]();v.length=0,v.push.apply(v,_),_.length=0})},writeCacheFile:function(e){g?e&&v.push(e):(g=setTimeout(this._write.bind(this),this.writeFileInterval),!0===m?e&&_.push(e):e&&v.push(e))},_cache:function(){var t,n=this;for(t in this.cacheQueue){var e=function(e){if(p=!1,e){if(b.test(e.message))return n.outOfStorage=!0,void(n.autoClear&&n.clearLRU())}else n.cachedFiles.add(t,{bundle:r,url:s,lastTime:c}),delete n.cacheQueue[t],n.writeCacheFile();cc.js.isEmptyObject(n.cacheQueue)||(p=!0,setTimeout(n._cache.bind(n),n.cacheInterval))},i=this.cacheQueue[t],o=i.srcUrl,a=i.isCopy,r=i.cacheBundleRoot,c=Date.now().toString(),s="",s=(r?"".concat(this.cacheDir,"/").concat(r,"/"):"".concat(this.cacheDir,"/")).concat(c).concat(w++).concat(cc.path.extname(t));return void(a?l(o,s,e):u(o,s,null,e))}p=!1},cacheFile:function(e,t,n,i,o){!(n=void 0!==n?n:this.cacheEnabled)||this.cacheQueue[e]||this.cachedFiles.has(e)||(this.cacheQueue[e]={srcUrl:t,cacheBundleRoot:i,isCopy:o},p||(p=!0,this.outOfStorage?p=!1:setTimeout(this._cache.bind(this),this.cacheInterval)))},clearCache:function(){var t=this,e=(f(this.cacheDir,!0),this.cachedFiles=new cc.AssetManager.Cache,c(this.cacheDir,!0),this.cacheDir+"/"+this.cachedFileName);this.outOfStorage=!1,r(e,JSON.stringify({files:this.cachedFiles._map,version:this.version}),"utf8"),cc.assetManager.bundles.forEach(function(e){F.test(e.base)&&t.makeBundleFolder(e.name)})},clearLRU:function(){if(!y){y=!0;var n=[],i=this;if(this.cachedFiles.forEach(function(e,t){"internal"===e.bundle||i._isZipFile(t)&&cc.assetManager.bundles.has(e.bundle)||n.push({originUrl:t,url:e.url,lastTime:e.lastTime})}),n.sort(function(e,t){return e.lastTime-t.lastTime}),n.length=Math.floor(this.cachedFiles.count/3),0!==n.length){for(var e=0,t=n.length;e<t;e++)this.cachedFiles.remove(n[e].originUrl);this.writeCacheFile(function(){setTimeout(function e(){var t=n.pop();i._isZipFile(t.originUrl)?(f(t.url,!0),i._deleteFileCB()):d(t.url,i._deleteFileCB.bind(i)),0<n.length?setTimeout(e,i.deleteInterval):y=!1},i.deleteInterval)})}}},removeCache:function(e){var t,n;this.cachedFiles.has(e)&&(n=(t=this).cachedFiles.remove(e).url,this.writeCacheFile(function(){t._isZipFile(e)?(f(n,!0),t._deleteFileCB()):d(n,t._deleteFileCB.bind(t))}))},_deleteFileCB:function(e){e||(this.outOfStorage=!1)},makeBundleFolder:function(e){c(this.cacheDir+"/"+e,!0)},unzipAndCacheBundle:function(t,e,n,i){var o=Date.now().toString(),a="".concat(this.cacheDir,"/").concat(n,"/").concat(o).concat(w++),r=this;c(a,!0),h(e,a,function(e){if(e)return f(a,!0),void(i&&i(e));r.cachedFiles.add(t,{bundle:n,url:a,lastTime:o}),r.writeCacheFile(),i&&i(null,a)})},_isZipFile:function(e){return".zip"===e.slice(-4)}}},{}],3:[function(e,t,n){"use strict";e("./rt-sys.js"),e("./rt_input.js"),e("./rt-game.js"),e("./rt-jsb.js"),e("./jsb-node.js"),e("./jsb-audio.js"),e("./AssetManager.js"),e("./jsb-editbox.js")},{"./AssetManager.js":1,"./jsb-audio.js":4,"./jsb-editbox.js":5,"./jsb-node.js":6,"./rt-game.js":8,"./rt-jsb.js":9,"./rt-sys.js":10,"./rt_input.js":11}],4:[function(e,t,n){"use strict";function i(e){return void 0===e?e=1:"string"==typeof e&&(e=Number.parseFloat(e)),e}var o,a,r,c,s=cc._Audio=function(e){this.src=e,this.volume=1,this.loop=!1,this.id=-1};o=s.prototype,(a=__globalAdapter.AudioEngine)&&(cc.audioEngine=a,cc.audioEngine.getMaxAudioInstance=function(){return 13},a.setMaxWebAudioSize=function(){},s.State=a.AudioState,o.play=function(){a.stop(this.id);var e=this.src;this.id=a.play(e,this.loop,this.volume)},o.pause=function(){a.pause(this.id)},o.resume=function(){a.resume(this.id)},o.stop=function(){a.stop(this.id)},o.destroy=function(){},o.setLoop=function(e){this.loop=e,a.setLoop(this.id,e)},o.getLoop=function(){return this.loop},o.setVolume=function(e){return e=i(e),this.volume=e,a.setVolume(this.id,e)},o.getVolume=function(){return this.volume},o.setCurrentTime=function(e){a.setCurrentTime(this.id,e)},o.getCurrentTime=function(){return a.getCurrentTime(this.id)},o.getDuration=function(){return a.getDuration(this.id)},r={id:-1,clip:"",loop:!(o.getState=function(){return a.getState(this.id)}),volume:1},c={volume:1},a.play=function(e,t,n){var i;return"number"!=typeof n&&(n=1),"string"==typeof e?(cc.warnID(8401,"cc.audioEngine","cc.AudioClip","AudioClip","cc.AudioClip","audio"),i=e):e.loaded?i="string"==typeof e._nativeAsset?e._nativeAsset:e._nativeAsset.src:(e._nativeAsset=i=e.nativeUrl,e.loaded=!0),a.play2d(i,t,n)},a.playMusic=function(e,t){return a.stop(r.id),r.id=a.play(e,t,r.volume),r.loop=t,r.clip=e,r.id},a.stopMusic=function(){a.stop(r.id)},a.pauseMusic=function(){return a.pause(r.id),r.id},a.resumeMusic=function(){return a.resume(r.id),r.id},a.getMusicVolume=function(){return r.volume},a.setMusicVolume=function(e){return r.volume=i(e),a.setVolume(r.id,r.volume),e},a.isMusicPlaying=function(){return a.getState(r.id)===a.AudioState.PLAYING},a.playEffect=function(e,t){return a.play(e,t||!1,c.volume)},a.setEffectsVolume=function(e){c.volume=i(e)},a.getEffectsVolume=function(){return c.volume},a.pauseEffect=function(e){return a.pause(e)},a.pauseAllEffects=function(){var e=a.getState(r.id)===a.AudioState.PLAYING;a.pauseAll(),e&&a.resume(r.id)},a.resumeEffect=function(e){a.resume(e)},a.resumeAllEffects=function(){var e=a.getState(r.id)===a.AudioState.PAUSED;a.resumeAll(),e&&a.getState(r.id)===a.AudioState.PLAYING&&a.pause(r.id)},a.stopEffect=function(e){return a.stop(e)},a.stopAllEffects=function(){var e=a.getState(r.id)===a.AudioState.PLAYING,t=a.getCurrentTime(r.id);a.stopAll(),e&&(r.id=a.play(r.clip,r.loop),a.setCurrentTime(r.id,t))},a._break=function(){},a._restore=function(){},a._uncache=a.uncache,a.uncache=function(e){var t;if("string"==typeof e)cc.warnID(8401,"cc.audioEngine","cc.AudioClip","AudioClip","cc.AudioClip","audio"),t=e;else{if(!e)return;t=e._nativeAsset}a._uncache(t)},a._preload=a.preload,a.preload=function(e,t){cc.warn("`cc.audioEngine.preload` is deprecated, use `cc.assetManager.loadRes(url, cc.AudioClip)` instead please."),a._preload(e,t)})},{}],5:[function(e,t,n){"use strict";function i(){s.call(this),this._eventListeners={onKeyboardInput:null,onKeyboardConfirm:null,onKeyboardComplete:null}}var o,a,r,c,s;cc&&cc.EditBox&&(o=cc.EditBox,a=cc.js,r=o.KeyboardReturnType,c=null,s=o._ImplClass,a.extend(i,s),o._ImplClass=i,Object.assign(i.prototype,{init:function(e){e?this._delegate=e:cc.error("EditBox init failed")},beginEditing:function(){var e;c!==this&&(e=this._delegate,c?(c._eventListeners.onKeyboardComplete(),__globalAdapter.updateKeyboard&&__globalAdapter.updateKeyboard({value:e._string})):this._showKeyboard(),this._registerKeyboardEvent(),this._editing=!0,c=this,e.editBoxEditingDidBegan())},endEditing:function(){this._hideKeyboard();var e=this._eventListeners;e.onKeyboardComplete&&e.onKeyboardComplete()},_registerKeyboardEvent:function(){var n=this,i=this._delegate,e=this._eventListeners;e.onKeyboardInput=function(e){i._string!==e.value&&i.editBoxTextChanged(e.value)},e.onKeyboardConfirm=function(e){i.editBoxEditingReturn();var t=n._eventListeners;t.onKeyboardComplete&&t.onKeyboardComplete()},e.onKeyboardComplete=function(){n._editing=!1,c=null,n._unregisterKeyboardEvent(),i.editBoxEditingDidEnded()},__globalAdapter.onKeyboardInput(e.onKeyboardInput),__globalAdapter.onKeyboardConfirm(e.onKeyboardConfirm),__globalAdapter.onKeyboardComplete(e.onKeyboardComplete)},_unregisterKeyboardEvent:function(){var e=this._eventListeners;e.onKeyboardInput&&(__globalAdapter.offKeyboardInput(e.onKeyboardInput),e.onKeyboardInput=null),e.onKeyboardConfirm&&(__globalAdapter.offKeyboardConfirm(e.onKeyboardConfirm),e.onKeyboardConfirm=null),e.onKeyboardComplete&&(__globalAdapter.offKeyboardComplete(e.onKeyboardComplete),e.onKeyboardComplete=null)},_showKeyboard:function(){var e=this._delegate,t=e.inputMode===o.InputMode.ANY,n=e.maxLength<0?65535:e.maxLength;__globalAdapter.showKeyboard({defaultValue:e._string,maxLength:n,multiple:t,confirmHold:!1,confirmType:function(e){switch(e){case r.DEFAULT:case r.DONE:return"done";case r.SEND:return"send";case r.SEARCH:return"search";case r.GO:return"go";case r.NEXT:return"next"}return"done"}(e.returnType),success:function(e){},fail:function(e){cc.warn(e.errMsg)}})},_hideKeyboard:function(){__globalAdapter.hideKeyboard({success:function(e){},fail:function(e){cc.warn(e.errMsg)}})}}))},{}],6:[function(e,t,n){"use strict";new Float32Array(16);var i=new cc.Mat4;cc.Node.prototype.getWorldRTInAB=function(){return this.getWorldRT(i),i.m},cc.Node.prototype.getWorldMatrixInAB=function(){return this._updateWorldMatrix(),this._worldMatrix.m}},{}],7:[function(e,t,n){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(){var e=window.__globalAdapter;if("object"!=o(e))return console.error("not quick game platform"),"break";if("object"!==("undefined"==typeof cc?"undefined":o(cc)))return console.error("can not get cocos creator version"),"break";var t=cc.ENGINE_VERSION,t=+"2.0.9".replace(/[a-zA-Z]/g,function(e,t){return e.charCodeAt()}).replace(/[^\d]/g,"")<=+t.replace(/[a-zA-Z]/g,function(e,t){return e.charCodeAt()}).replace(/[^\d]/g,""),n="function"==typeof e.getFeature&&"function"==typeof e.setFeature,i="canvas.context2d.premultiply_image_data",s=!1;if(!t&&!n)return"break";if(t||(s=!0),n&&(e.setFeature(i,s),e.getFeature(i)===s))return"break";var t=Object.getOwnPropertyDescriptor(HTMLCanvasElement.prototype,"_data"),l=void 0;"object"===o(t)&&(l=t.get),delete HTMLCanvasElement.prototype._data,Object.defineProperty(HTMLCanvasElement.prototype,"_data",{get:function(){var e="function"==typeof l?l.bind(this)():this._dataInner;if(null===e)return null;var t=e._premultHandled;if(!0!==t){var n,i=e;if(s)for(var o=0,a=i._data.length;o<a;o+=4)0<(n=i._data[o+3]/255)&&n<255&&(i._data[o+0]=clamp(i._data[o+0]*n),i._data[o+1]=clamp(i._data[o+1]*n),i._data[o+2]=clamp(i._data[o+2]*n));else for(var r=0,c=i._data.length;r<c;r+=4)0<(n=255/i._data[r+3])&&n<255&&(i._data[r+0]=u(i._data[r+0]*n),i._data[r+1]=u(i._data[r+1]*n),i._data[r+2]=u(i._data[r+2]*n));e._premultHandled=!0}return e},set:function(e){this._dataInner=e}})}var u=function(e){return(e=Math.round(e))<0?0:e<255?e:255};do{}while("break"!==i()&&0)},{}],8:[function(e,t,n){"use strict";cc.game.restart=function(){cc.sys.restartVM()},__globalAdapter.onHide(function(){cc.game.emit(cc.game.EVENT_HIDE)}),__globalAdapter.onShow(function(){cc.game.emit(cc.game.EVENT_SHOW)}),__globalAdapter.onWindowResize&&__globalAdapter.onWindowResize(function(){cc.game.canvas&&cc.view.setCanvasSize(window.innerWidth,window.innerHeight)})},{}],9:[function(e,t,n){"use strict";var i=window.fsUtils,a=i.fs,o=i.readJsonSync,r=i.readArrayBufferSync,c=i.writeFileSync,s=__globalAdapter;jsb.fileUtils={getStringFromFile:function(e){return o(e)},getDataFromFile:function(e){return r(e)},getWritablePath:function(){return"".concat(s.env.USER_DATA_PATH,"/")},writeToFile:function(e,t){var e=JSON.stringify(e),n=!1;try{c(t,e,"utf8"),n=!0}catch(e){throw cc.error(e),new Error("writeToFile fail:",e)}return n},getValueMapFromFile:function(e){var t;try{t=o(e)}catch(e){return cc.error(e),{}}return t}},void 0===jsb.saveImageData&&s.saveImageTempSync&&a.saveFileSync&&(jsb.saveImageData=function(e,t,n,i){var o=i.lastIndexOf(".");if(-1===o)return!1;o=i.substr(o+1),e=s.saveImageTempSync({data:e,width:t,height:n,fileType:o});return""!==e&&a.saveFileSync(e,i)===i}),void 0===jsb.setPreferredFramesPerSecond&&(void 0!==s.setPreferredFramesPerSecond?jsb.setPreferredFramesPerSecond=s.setPreferredFramesPerSecond:jsb.setPreferredFramesPerSecond=function(){console.error("The jsb.setPreferredFramesPerSecond is not define!")})},{}],10:[function(e,t,n){"use strict";var i,o,a=cc.sys;"function"==typeof __globalAdapter.getNetworkType?a.getNetworkType=__globalAdapter.getNetworkType:a.getNetworkType=function(){console.error("The sys.getNetworkType is not define!")},"function"==typeof __globalAdapter.getBatteryLevel?a.getBatteryLevel=__globalAdapter.getBatteryLevel:a.getBatteryLevel=function(){console.error("The sys.getBatteryLevel is not define!")},"function"==typeof __globalAdapter.triggerGC?a.garbageCollect=__globalAdapter.triggerGC:a.garbageCollect=function(){console.error("The sys.garbageCollect is not define!")},a.restartVM=function(){console.error("The restartVM is not define!")},a.isObjectValid=function(){console.error("The sys.isObjectValid is not define!")},a.isBrowser=!1,a.isMobile=!0,"function"==typeof __globalAdapter.getSystemInfoSync&&(i=void 0,(o=__globalAdapter.getSystemInfoSync()).language?i=o.language:"undefined"!=typeof __getCurrentLanguage&&(i=__getCurrentLanguage()),a.language=i.substr(0,2),a.languageCode=i.toLowerCase(),i=o.system.toLowerCase(),o=/[\d\.]+/.exec(i="android p"===i?"android p 9.0":i),a.osVersion=o?o[0]:i,a.osMainVersion=parseInt(a.osVersion),a.browserType=null,a.browserVersion=null)},{}],11:[function(e,t,n){"use strict";jsb.inputBox={onConfirm:function(e){__globalAdapter.onKeyboardConfirm(e)},offConfirm:function(e){__globalAdapter.offKeyboardConfirm(e)},onComplete:function(e){__globalAdapter.onKeyboardComplete(e)},offComplete:function(e){__globalAdapter.offKeyboardComplete(e)},onInput:function(e){__globalAdapter.onKeyboardInput(e)},offInput:function(e){__globalAdapter.offKeyboardInput(e)},show:function(e){__globalAdapter.showKeyboard(e)},hide:function(){__globalAdapter.hideKeyboard()}}},{}],12:[function(e,t,n){"use strict";var i,o,a,r;jsb.device&&!jsb.device.getDeviceMotionValue&&(r=void(a=o=i=0),jsb.device.setAccelerometerEnabled=function(e){if(void 0!==r!==e){if(!e)return qg.stopAccelerometer(),void(r=void(a=o=i=0));r=new Array(6).fill(0),qg.onAccelerometerChange(function(e){r[3]=1.25*e.x+i,r[4]=1.25*e.y+o,r[5]=1.25*e.z+a,i=.8*i+.2*r[3],o=.8*o+.2*r[4],a=.8*a+.2*r[5]})}},jsb.device.getDeviceMotionValue=function(){if(void 0!==r)return r.slice()})},{}],13:[function(t,n,i){"use strict";var o=qg.getFileSystemManager?qg.getFileSystemManager():null,r={fs:o,_subpackagesPath:"usr_",getUserDataPath:function(){return qg.env.USER_DATA_PATH},checkFsValid:function(){return!!o||(console.warn("can not get the file system!"),!1)},deleteFile:function(t,n){o.unlink({filePath:t,success:function(){n&&n(null)},fail:function(e){console.warn("Delete file failed: path: ".concat(t," message: ").concat(e.errMsg)),n&&n(new Error(e.errMsg))}})},downloadFile:function(t,e,n,i,o){var a={url:t,success:function(e){200===e.statusCode?o&&o(null,e.tempFilePath||e.filePath):(e.filePath&&r.deleteFile(e.filePath),console.warn("Download file failed: path: ".concat(t," message: ").concat(e.statusCode)),o&&o(new Error(e.statusCode),null))},fail:function(e){console.warn("Download file failed: path: ".concat(t," message: ").concat(e.errMsg)),o&&o(new Error(e.errMsg),null)}},e=(e&&(a.filePath=e),n&&(a.header=n),qg.downloadFile(a));i&&e.onProgressUpdate(i)},saveFile:function(t,e,n){o.saveFile({tempFilePath:t,filePath:e,success:function(e){n&&n(null)},fail:function(e){console.warn("Save file failed: path: ".concat(t," message: ").concat(e.errMsg)),n&&n(new Error(e.errMsg))}})},copyFile:function(t,e,n){o.copyFile({srcPath:t,destPath:e,success:function(){n&&n(null)},fail:function(e){console.warn("Copy file failed: path: ".concat(t," message: ").concat(e.errMsg)),n&&n(new Error(e.errMsg))}})},writeFile:function(t,e,n,i){o.writeFile({filePath:t,encoding:n,data:e,success:function(){i&&i(null)},fail:function(e){console.warn("Write file failed: path: ".concat(t," message: ").concat(e.errMsg)),i&&i(new Error(e.errMsg))}})},writeFileSync:function(t,e,n){try{return o.writeFileSync(t,e,n),null}catch(e){return console.warn("Write file failed: path: ".concat(t," message: ").concat(e.message)),new Error(e.message)}},readFile:function(t,e,n){o.readFile({filePath:t,encoding:e,success:function(e){n&&n(null,e.data)},fail:function(e){console.warn("Read file failed: path: ".concat(t," message: ").concat(e.errMsg)),n&&n(new Error(e.errMsg),null)}})},readDir:function(t,n){o.readdir({dirPath:t,success:function(e){n&&n(null,e.files)},fail:function(e){console.warn("Read directory failed: path: ".concat(t," message: ").concat(e.errMsg)),n&&n(new Error(e.errMsg),null)}})},readText:function(e,t){r.readFile(e,"utf8",t)},readArrayBuffer:function(e,t){r.readFile(e,"",t)},readArrayBufferSync:function(t){try{return o.readFileSync(t,"binary")}catch(e){return console.warn("Read json failed: path: ".concat(t," message: ").concat(e.message)),new Error(e)}},readJson:function(i,o){r.readFile(i,"utf8",function(t,e){var n=null;if(!t)try{n=JSON.parse(e)}catch(e){console.warn("Read json failed: path: ".concat(i," message: ").concat(e.message)),t=new Error(e.message)}o&&o(t,n)})},readJsonSync:function(t){try{var e=o.readFileSync(t,"utf8");return JSON.parse(e)}catch(e){return console.warn("Read json failed: path: ".concat(t," message: ").concat(e.message)),new Error(e)}},makeDirSync:function(t,e){try{return o.mkdirSync(t,e),null}catch(e){return console.warn("Make directory failed: path: ".concat(t," message: ").concat(e.message)),new Error(e)}},rmdirSync:function(t,e){try{o.rmdirSync(t,e)}catch(e){return console.warn("rm directory failed: path: ".concat(t," message: ").concat(e.message)),new Error(e)}},exists:function(e,t){o.access({path:e,success:function(){t&&t(!0)},fail:function(){t&&t(!1)}})},existsSync:function(t){try{o.accessSync(t)}catch(t){return new Error(e)}},loadSubpackage:function(t,e,n){var i=qg.loadSubpackage({name:r._subpackagesPath+t,success:function(){n&&n()},fail:function(e){console.warn("Load Subpackage failed: path: ".concat(t," message: ").concat(e.errMsg)),n&&n(new Error("Failed to load subpackage ".concat(t,": ").concat(e.errMsg)))}});return e&&i.onProgressUpdate(e),i},unzip:function(t,e,n){o.unzip({zipFilePath:t,targetPath:e,success:function(){n&&n(null)},fail:function(e){console.warn("unzip failed: path: ".concat(t," message: ").concat(e.errMsg)),n&&n(new Error("unzip failed: "+e.errMsg))}})}};cc.assetManager.fsUtils=window.fsUtils=n.exports=r},{}],14:[function(e,t,n){"use strict";do{if("undefined"==typeof qg){console.error("not quick game platform");break}}while(window.__globalAdapter=qg,e("../../../common/engine/rt-feature-premut-alpha.js"),e("./fs-utils.js"),e("../../../common/engine/index.js"),e("./DeviceMotionEvent.js"),e("./rt-videoplayer.js"),0)},{"../../../common/engine/index.js":3,"../../../common/engine/rt-feature-premut-alpha.js":7,"./DeviceMotionEvent.js":12,"./fs-utils.js":13,"./rt-videoplayer.js":15}],15:[function(e,t,n){"use strict";function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var r,l,u,d,c,i;"undefined"!=typeof loadRuntime&&(r=loadRuntime(),cc&&cc.VideoPlayer&&cc.VideoPlayer.Impl&&(l=cc.Mat4,u=new cc.Mat4,d=new cc.Mat4,c=cc.VideoPlayer.Impl,i=cc.VideoPlayer.Impl.prototype,cc.VideoPlayer.prototype._updateVideoSource=function(){var t=this,n=this._clip;this.resourceType===cc.VideoPlayer.ResourceType.REMOTE?this._impl.setURL(this.remoteURL,this._mute||0===this._volume):n&&(n._nativeAsset?this._impl.setURL(n._nativeAsset,this._mute||0===this._volume):cc.assetManager.postLoadNative(n,function(e){e?console.error(e.message,e.stack):t._impl.setURL(n._nativeAsset,t._mute||0===t._volume)}))},i._bindEvent=function(){var e=this._video,n=this;e&&(e.onPlay(function(){n._video===e&&(n._playing=!0,n._dispatchEvent(c.EventType.PLAYING))}),e.onEnded(function(){n._video===e&&(n._playing=!1,n._currentTime=n._duration,n._dispatchEvent(c.EventType.COMPLETED))}),e.onPause(function(){n._video===e&&(n._playing=!1,n._dispatchEvent(c.EventType.PAUSED))}),e.onTimeUpdate(function(e){var t=JSON.parse(e.position);if("object"===a(t))return n._duration=t.duration,void(n._currentTime=t.position);n._duration=e.duration,n._currentTime=e.position}))},i._unbindEvent=function(){var e=this._video;e&&(e.offPlay(),e.offEnded(),e.offPause(),e.offTimeUpdate())},i.setVisible=function(e){var t=this._video;t&&(t.width=e&&this._actualWidth||0,this._visible=e)},i.createDomElementIfNeeded=function(){r.createVideo||cc.warn("VideoPlayer not supported")},i.setURL=function(e,t){var n,i,o=this._video;o&&o.src===e||this._tx&&(this._video&&(this.destroy(),this._video=null),e="string"!=typeof e?e._audio:e,this._duration=-1,this._currentTime=-1,this._video=r.createVideo({x:this._tx,y:this._ty,width:this._width,height:this._height,src:e,objectFit:"contain",live:!1}),(o=this._video).src=e,o.muted=!0,(n=this)._loaded=!1,i=function(e){var t=JSON.parse(e.position);if("object"===a(t))return n._duration=t.duration,void(n._currentTime=t.position);n._duration=e.duration,n._currentTime=e.position},o.onPlay(function e(){o.offPlay(e),o.offTimeUpdate(i),n.enable(),n._bindEvent(),o.stop(),o.muted=!1,n._loaded=!0,n._playing=!1,n._currentTime=0,n._dispatchEvent(c.EventType.READY_TO_PLAY)}),o.onTimeUpdate(i),o.play())},i.getURL=function(){var e=this._video;return e?e.src:""},i.play=function(){var e=this._video;e&&this._visible&&!this._playing&&e.play()},i.setStayOnBottom=function(e){},i.pause=function(){var e=this._video;this._playing&&e&&e.pause()},i.resume=function(){var e=this._video;!this._playing&&e&&e.play()},i.stop=function(){var e=this._video;e&&this._visible&&(e.stop(),this._dispatchEvent(c.EventType.STOPPED),this._playing=!1)},i.setVolume=function(e){},i.seekTo=function(e){var t=this._video;t&&this._loaded&&t.seek(e)},i.isPlaying=function(){return this._playing},i.duration=function(){return this._duration},i.currentTime=function(){return this._currentTime||-1},i.setKeepAspectRatioEnabled=function(e){cc.log("On wechat game is always keep the aspect ratio")},i.isKeepAspectRatioEnabled=function(){return!0},i.isFullScreenEnabled=function(){return this._fullScreenEnabled},i.setFullScreenEnabled=function(e){var t=this._video;t&&(t.exitFullScreen(),e&&t.requestFullScreen(),this._fullScreenEnabled=e)},i.enable=function(){this.setVisible(!0)},i.disable=function(){this.setVisible(!1)},i.destroy=function(){this.disable(),this._unbindEvent(),this.stop(),this._video&&(this._video.destroy(),this._video=void 0),r.triggerGC()},i.updateMatrix=function(e){var t,n,i,o,a,r,c,s;e.getWorldMatrix(u),this._m00===u.m[0]&&this._m01===u.m[1]&&this._m04===u.m[4]&&this._m05===u.m[5]&&this._m12===u.m[12]&&this._m13===u.m[13]&&this._w===e._contentSize.width&&this._h===e._contentSize.height||(this._m00=u.m[0],this._m01=u.m[1],this._m04=u.m[4],this._m05=u.m[5],this._m12=u.m[12],this._m13=u.m[13],this._w=e._contentSize.width,this._h=e._contentSize.height,cc.Camera.findCamera(e).getWorldToScreenMatrix2D(d),l.multiply(d,d,u),c=cc.view._scaleX,t=cc.view._scaleY,r=cc.view._devicePixelRatio,n=d.m[0]*(c/=r),i=d.m[5]*(t/=r),n=this._w*n,i=this._h*i,s=n*e._anchorPoint.x,e=i*e._anchorPoint.y,o=(a=cc.view._viewportRect).x/r,a=a.y/r,r=d.m[12]*c-s+o,c=d.m[13]*t-e+a,s=cc.view.getFrameSize().height,this._tx=r,this._ty=c,this._width=n,this._height=i,this._video&&(this._video.x=r,this._video.y=s-i-c,this._video.width=n,this._video.height=i),this._actualWidth=n)}))},{}]},{},[14]);